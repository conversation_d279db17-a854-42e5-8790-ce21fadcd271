#!/usr/bin/env python3
"""
Video File Processor for Shoplifting Detection
Processes pre-recorded video files instead of live webcam feed
"""

import cv2
import os
import time
import argparse
from pathlib import Path
from typing import Optional, List, Dict
from collections import defaultdict, deque

# Import existing utilities
from utils.detection import detect_persons
from utils.cropping import crop_person
from utils.models import load_action_transformer
import torch

# ============================================================================
# 📹 SPECIFY YOUR VIDEO PATH HERE:
# ============================================================================
DEFAULT_VIDEO_PATH = "data/feed/Untitled Project.mp4"  # ← Change this to your video file path
# Examples:
# DEFAULT_VIDEO_PATH = "C:/Videos/security_footage.mp4"
# DEFAULT_VIDEO_PATH = "data/videos/test_video.mp4"
# DEFAULT_VIDEO_PATH = "sample_video.mp4"
# ============================================================================

# ============================================================================
# 📺 DISPLAY WINDOW SETTINGS:
# ============================================================================
DISPLAY_WINDOW_WIDTH = 1920   # Display window width in pixels
DISPLAY_WINDOW_HEIGHT = 600  # Display window height in pixels
DISPLAY_FRAMERATE = 25       # Target framerate for video display (FPS)
SHOW_PERSON_IDS = False       # Show person ID numbers in video display
# ============================================================================


class VideoFileProcessor:
    """
    Process pre-recorded video files for shoplifting detection
    Similar to LiveVideoProcessor but works with video files
    """
    
    def __init__(
        self,
        video_path: str,
        model_path: str = 'data/models/trained_model_cnn.pt',
        yolo_path: str = 'data/models/yolo11s.pt',
        output_dir: str = 'data/output/video_processing',
        frame_buffer_size: int = 16,
        crop_size: tuple = (224, 224),
        confidence_threshold: float = 0.1,
        skip_frames: int = 0
    ):
        """
        Initialize video file processor
        
        Args:
            video_path: Path to input video file
            model_path: Path to 3DCNN model
            yolo_path: Path to YOLO model
            output_dir: Directory to save results
            frame_buffer_size: Number of frames per clip
            crop_size: Size for person crops
            confidence_threshold: YOLO confidence threshold
            skip_frames: Number of frames to skip between processing (for speed)
        """
        self.video_path = video_path
        self.model_path = model_path
        self.yolo_path = yolo_path
        self.output_dir = output_dir
        self.frame_buffer_size = frame_buffer_size
        self.crop_size = crop_size
        self.confidence_threshold = confidence_threshold
        self.skip_frames = skip_frames
        
        # Validate input video
        if not os.path.exists(video_path):
            raise FileNotFoundError(f"Video file not found: {video_path}")
        
        # Create output directories
        self.session_timestamp = time.strftime("%d-%m-%Y_%H-%M-%S")
        video_name = Path(video_path).stem
        self.session_dir = os.path.join(output_dir, f"{video_name}_{self.session_timestamp}")
        self.feed_dir = os.path.join(self.session_dir, "feed")
        self.annotated_dir = os.path.join(self.session_dir, "annotated")
        
        os.makedirs(self.feed_dir, exist_ok=True)
        os.makedirs(self.annotated_dir, exist_ok=True)
        
        print(f"📅 Processing session: {self.session_timestamp}")
        print(f"📁 Session directory: {self.session_dir}")
        print(f"📹 Input video: {video_path}")
        
        # Initialize models - Force CUDA usage
        if not torch.cuda.is_available():
            raise RuntimeError("❌ CUDA is not available! This application requires a CUDA-capable GPU.")

        self.device = torch.device('cuda:0')
        print(f"🚀 Using device: {self.device} ({torch.cuda.get_device_name(0)})")

        # Load models
        print("Loading models...")
        self.action_model = load_action_transformer(model_path, self.device)

        # Load YOLO model and move to CUDA
        from ultralytics import YOLO
        self.yolo_model = YOLO(yolo_path)
        self.yolo_model.to(self.device)  # Ensure YOLO uses CUDA
        print("✅ Models loaded successfully on CUDA!")
        
        # Statistics
        self.frame_count = 0
        self.clip_count = 0
        self.prediction_count = 0
        self.person_count = 0

        # Performance tracking
        self.inference_times = []  # Track individual inference times
        self.yolo_times = []       # Track YOLO detection times
        self.total_inference_time = 0.0
        self.total_yolo_time = 0.0
        self.frame_processing_times = []  # Track per-frame processing times

        # Processing data
        self.frame_buffer = deque(maxlen=frame_buffer_size)
        self.all_results = []
        
    def _detect_persons(self, frame):
        """Detect persons in frame using YOLO"""
        start_time = time.time()
        result = detect_persons(self.yolo_model, frame, self.confidence_threshold)
        yolo_time = time.time() - start_time

        # Track YOLO performance
        self.yolo_times.append(yolo_time)
        self.total_yolo_time += yolo_time

        return result

    def _crop_person(self, frame, box):
        """Crop person from frame"""
        return crop_person(frame, box, self.crop_size)
    
    def _predict_clip(self, clip_path: str) -> float:
        """Make prediction on video clip with timing"""
        from utils.prediction import predict_with_timing

        prediction, inference_time = predict_with_timing(self.action_model, clip_path, self.device, warmup_runs=2)

        # Track inference performance
        self.inference_times.append(inference_time)
        self.total_inference_time += inference_time

        return prediction
    
    def _save_clip(self, frames: List, timestamp: str, clip_type: str = "feed") -> str:
        """Save frames as video clip"""
        if clip_type == "feed":
            filename = f"clip_{timestamp}.mp4"
            output_path = os.path.join(self.feed_dir, filename)
        else:
            filename = f"annotated_{timestamp}.mp4"
            output_path = os.path.join(self.annotated_dir, filename)
        
        if not frames:
            return None
            
        h, w = frames[0].shape[:2]
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(output_path, fourcc, 16.0, (w, h))  # 16 FPS for individual clips
        
        for frame in frames:
            out.write(frame)
        
        out.release()
        return output_path
    
    def _save_person_clip(self, crops: List, person_id: int, timestamp: str) -> str:
        """Save person crops as video clip"""
        filename = f"person_{person_id}_{timestamp}.mp4"
        output_path = os.path.join(self.feed_dir, filename)
        
        if not crops:
            return None
            
        h, w = crops[0].shape[:2]
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(output_path, fourcc, 16.0, (w, h))  # 16 FPS for individual clips
        
        for crop in crops:
            out.write(crop)
        
        out.release()
        return output_path
    
    def process_video(self, display_progress: bool = True, save_annotated: bool = True,
                     show_realtime: bool = True, playback_speed: float = 1.0) -> Dict:
        """
        Process the entire video file

        Args:
            display_progress: Whether to show processing progress
            save_annotated: Whether to save annotated clips
            show_realtime: Whether to display video in real-time with predictions
            playback_speed: Speed multiplier for real-time display (1.0 = normal speed)

        Returns:
            Dictionary with processing results and statistics
        """
        print(f"\n🎬 Starting video processing...")
        print(f"📊 Settings: {self.frame_buffer_size} frames/clip, confidence: {self.confidence_threshold}")
        
        # Open video file
        cap = cv2.VideoCapture(self.video_path)
        if not cap.isOpened():
            raise ValueError(f"Could not open video file: {self.video_path}")
        
        # Get video properties
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        fps = cap.get(cv2.CAP_PROP_FPS)
        duration = total_frames / fps if fps > 0 else 0
        
        print(f"📹 Video info: {total_frames} frames, {fps:.1f} FPS, {duration:.1f}s duration")
        
        start_time = time.time()
        frame_skip_counter = 0

        # Real-time display setup
        if show_realtime:
            cv2.namedWindow('Video Processing - Real-time', cv2.WINDOW_NORMAL)
            cv2.resizeWindow('Video Processing - Real-time', DISPLAY_WINDOW_WIDTH, DISPLAY_WINDOW_HEIGHT)
            # Use specified display framerate instead of video's original framerate
            frame_delay = int((1000 / DISPLAY_FRAMERATE) / playback_speed)  # ms delay between frames
            print(f"🎬 Real-time display enabled (speed: {playback_speed}x) - Window: {DISPLAY_WINDOW_WIDTH}x{DISPLAY_WINDOW_HEIGHT}")
            print(f"📺 Display framerate: {DISPLAY_FRAMERATE} FPS | Press 'q' to quit, 'p' to pause/resume, 's' to save screenshot")

        # Processing state
        paused = False
        current_predictions = {}  # Store recent predictions for display

        try:
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                self.frame_count += 1
                
                # Skip frames if specified (for faster processing)
                if self.skip_frames > 0:
                    frame_skip_counter += 1
                    if frame_skip_counter <= self.skip_frames:
                        continue
                    frame_skip_counter = 0
                
                # Real-time display
                if show_realtime:
                    display_frame = self._create_display_frame(frame.copy(), current_predictions)
                    cv2.imshow('Video Processing - Real-time', display_frame)

                    # Handle keyboard input
                    key = cv2.waitKey(frame_delay) & 0xFF
                    if key == ord('q'):
                        print("🛑 User requested quit")
                        break
                    elif key == ord('p'):
                        paused = not paused
                        status = "⏸️ PAUSED" if paused else "▶️ RESUMED"
                        print(f"{status} - Press 'p' again to toggle")
                    elif key == ord('s'):
                        screenshot_path = f"screenshot_{int(time.time())}.jpg"
                        cv2.imwrite(screenshot_path, display_frame)
                        print(f"📸 Screenshot saved: {screenshot_path}")

                    # Handle pause
                    while paused and show_realtime:
                        key = cv2.waitKey(100) & 0xFF
                        if key == ord('p'):
                            paused = False
                            print("▶️ RESUMED")
                        elif key == ord('q'):
                            print("🛑 User requested quit")
                            break

                # Add frame to buffer
                self.frame_buffer.append(frame.copy())

                # Process when buffer is full
                if len(self.frame_buffer) == self.frame_buffer_size:
                    timestamp = f"{int(time.time() * 1000)}"

                    # Time the clip processing
                    clip_start_time = time.time()
                    clip_predictions = self._process_clip(list(self.frame_buffer), timestamp, save_annotated)
                    clip_processing_time = time.time() - clip_start_time

                    # Track frame processing time (per frame in the clip)
                    per_frame_time = clip_processing_time / self.frame_buffer_size
                    self.frame_processing_times.extend([per_frame_time] * self.frame_buffer_size)

                    # Update current predictions for display
                    if clip_predictions:
                        current_predictions.update(clip_predictions)
                        # Keep only recent predictions (last 5 seconds worth)
                        current_time = time.time()
                        current_predictions = {
                            k: v for k, v in current_predictions.items()
                            if current_time - v.get('timestamp', 0) < 5.0
                        }

                    # Clear buffer (or slide it for overlapping clips)
                    # For now, clear completely for non-overlapping clips
                    self.frame_buffer.clear()
                
                # Show progress
                if display_progress and self.frame_count % 100 == 0:
                    progress = (self.frame_count / total_frames) * 100
                    elapsed = time.time() - start_time
                    fps_processed = self.frame_count / elapsed if elapsed > 0 else 0
                    print(f"📊 Progress: {progress:.1f}% ({self.frame_count}/{total_frames} frames, "
                          f"{fps_processed:.1f} FPS, {self.clip_count} clips, {self.prediction_count} predictions)")
        
        finally:
            cap.release()
            if show_realtime:
                cv2.destroyAllWindows()
                print("📺 Real-time display closed")
        
        # Process remaining frames in buffer
        if len(self.frame_buffer) > 0:
            timestamp = f"{int(time.time() * 1000)}_final"
            self._process_clip(list(self.frame_buffer), timestamp, save_annotated)
        
        # Calculate final statistics
        processing_time = time.time() - start_time
        performance_stats = self.get_performance_stats()

        results = {
            'video_path': self.video_path,
            'session_dir': self.session_dir,
            'total_frames': total_frames,
            'frames_processed': self.frame_count,
            'clips_created': self.clip_count,
            'predictions_made': self.prediction_count,
            'persons_detected': self.person_count,
            'processing_time': processing_time,
            'fps_processed': self.frame_count / processing_time if processing_time > 0 else 0,
            'performance_stats': performance_stats,
            'all_results': self.all_results
        }

        print(f"\n✅ Video processing completed!")
        print(f"📊 Final stats: {self.frame_count} frames → {self.clip_count} clips → {self.prediction_count} predictions")
        print(f"⏱️  Processing time: {processing_time:.1f}s ({results['fps_processed']:.1f} FPS)")
        print(f"👥 Persons detected: {self.person_count}")

        # Print detailed performance statistics
        self._print_performance_stats(performance_stats, processing_time)

        return results

    def _create_display_frame(self, frame, current_predictions):
        """Create frame for real-time display with current predictions"""
        display_frame = frame.copy()

        # Get original frame dimensions
        orig_h, orig_w = frame.shape[:2]

        # Resize frame to specified display window size
        display_frame = cv2.resize(display_frame, (DISPLAY_WINDOW_WIDTH, DISPLAY_WINDOW_HEIGHT))

        # Calculate scaling factors for bounding box coordinates
        scale_x = DISPLAY_WINDOW_WIDTH / orig_w
        scale_y = DISPLAY_WINDOW_HEIGHT / orig_h

        # Detect persons in current frame for live display
        try:
            boxes, confidences, ids = self._detect_persons(frame)

            # Draw current detections
            for box, conf, person_id in zip(boxes, confidences, ids):
                # Scale bounding box coordinates to match resized frame
                x1, y1, x2, y2 = map(int, box)
                x1 = int(x1 * scale_x)
                y1 = int(y1 * scale_y)
                x2 = int(x2 * scale_x)
                y2 = int(y2 * scale_y)

                # Check if we have a recent prediction for this person
                prediction_score = None
                if str(person_id) in current_predictions:
                    prediction_score = current_predictions[str(person_id)].get('prediction', None)

                # Choose color based on prediction or default to blue for detection
                if prediction_score is not None:
                    if prediction_score > 0.5:
                        color = (0, 0, 255)  # Red for suspicious
                        if SHOW_PERSON_IDS:
                            label = f"Person {person_id}: SUSPICIOUS ({prediction_score:.2f})"
                        else:
                            label = f"SUSPICIOUS ({prediction_score:.2f})"
                    else:
                        color = (0, 255, 0)  # Green for normal
                        if SHOW_PERSON_IDS:
                            label = f"Person {person_id}: NORMAL ({prediction_score:.2f})"
                        else:
                            label = f"NORMAL ({prediction_score:.2f})"
                else:
                    color = (255, 0, 0)  # Blue for detection only
                    if SHOW_PERSON_IDS:
                        label = f"Person {person_id}: DETECTING..."
                    else:
                        label = "DETECTING..."

                # Draw bounding box
                cv2.rectangle(display_frame, (x1, y1), (x2, y2), color, 2)

                # Draw label background
                label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
                cv2.rectangle(display_frame, (x1, y1 - label_size[1] - 10),
                             (x1 + label_size[0], y1), color, -1)

                # Draw label text
                cv2.putText(display_frame, label, (x1, y1 - 5),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

                # Draw confidence
                conf_text = f"Conf: {conf:.2f}"
                cv2.putText(display_frame, conf_text, (x1, y2 + 20),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)

        except Exception as e:
            # If detection fails, just show the frame
            pass

        # Add processing info overlay
        info_text = f"Frame: {self.frame_count} | Clips: {self.clip_count} | Predictions: {self.prediction_count}"
        cv2.putText(display_frame, info_text, (10, DISPLAY_WINDOW_HEIGHT - 30),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

        # Add controls info
        controls_text = "Controls: 'q'=quit, 'p'=pause, 's'=screenshot"
        cv2.putText(display_frame, controls_text, (10, DISPLAY_WINDOW_HEIGHT - 10),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, (200, 200, 200), 1)

        return display_frame

    def _process_clip(self, frames: List, timestamp: str, save_annotated: bool = True):
        """Process a single clip for person detection and prediction"""
        # Save original clip
        clip_path = self._save_clip(frames, timestamp, "feed")
        self.clip_count += 1

        # Detect persons in all frames
        person_crops = defaultdict(list)
        person_boxes = defaultdict(list)
        person_confidences = defaultdict(list)
        frame_detections = []

        for frame_idx, frame in enumerate(frames):
            boxes, confidences, ids = self._detect_persons(frame)

            # Store frame-level detection data
            frame_detection = {
                'frame_idx': frame_idx,
                'boxes': boxes,
                'confidences': confidences,
                'ids': ids
            }
            frame_detections.append(frame_detection)

            # Extract crops for each detected person
            for box, conf, person_id in zip(boxes, confidences, ids):
                if conf > self.confidence_threshold:
                    crop = self._crop_person(frame, box)
                    person_crops[person_id].append(crop)
                    person_boxes[person_id].append(box)
                    person_confidences[person_id].append(conf)

        # Process each detected person
        person_predictions = {}
        for person_id, crops in person_crops.items():
            if len(crops) >= self.frame_buffer_size // 2:  # Require at least half the frames
                # Save person clip
                person_clip_path = self._save_person_clip(crops, person_id, timestamp)

                # Make prediction
                try:
                    prediction = self._predict_clip(person_clip_path)
                    person_predictions[person_id] = {
                        'prediction': prediction,
                        'box': person_boxes[person_id][-1],  # Latest box
                        'confidence': sum(person_confidences[person_id]) / len(person_confidences[person_id]),
                        'clip_path': person_clip_path
                    }

                    self.prediction_count += 1
                    self.person_count += 1

                    # Store result
                    result = {
                        'timestamp': timestamp,
                        'person_id': person_id,
                        'prediction': prediction,
                        'is_suspicious': prediction > 0.5,
                        'confidence': person_predictions[person_id]['confidence'],
                        'box': person_predictions[person_id]['box'],
                        'clip_path': clip_path,
                        'person_clip_path': person_clip_path
                    }
                    self.all_results.append(result)

                    # Print result
                    status = "🚨 SUSPICIOUS" if prediction > 0.5 else "✅ NORMAL"
                    print(f"{status} - Person {person_id} in clip {timestamp}: score {prediction:.3f}")

                except Exception as e:
                    print(f"❌ Error predicting person {person_id} in clip {timestamp}: {e}")

        # Save annotated clip if requested and we have predictions
        if save_annotated and person_predictions:
            try:
                annotated_path = self._save_annotated_clip(frames, person_predictions, timestamp, frame_detections)
                print(f"📹 Saved annotated clip: {annotated_path}")
            except Exception as e:
                print(f"❌ Error saving annotated clip for {timestamp}: {e}")

        # Return predictions for real-time display (with timestamp for expiry)
        display_predictions = {}
        for person_id, data in person_predictions.items():
            display_predictions[str(person_id)] = {
                'prediction': data['prediction'],
                'timestamp': time.time(),
                'box': data['box']
            }

        return display_predictions

    def get_performance_stats(self) -> Dict:
        """Calculate comprehensive performance statistics"""
        stats = {
            'inference': {
                'total_inferences': len(self.inference_times),
                'total_time': self.total_inference_time,
                'average_time': sum(self.inference_times) / len(self.inference_times) if self.inference_times else 0,
                'min_time': min(self.inference_times) if self.inference_times else 0,
                'max_time': max(self.inference_times) if self.inference_times else 0,
                'fps': len(self.inference_times) / self.total_inference_time if self.total_inference_time > 0 else 0
            },
            'yolo': {
                'total_detections': len(self.yolo_times),
                'total_time': self.total_yolo_time,
                'average_time': sum(self.yolo_times) / len(self.yolo_times) if self.yolo_times else 0,
                'min_time': min(self.yolo_times) if self.yolo_times else 0,
                'max_time': max(self.yolo_times) if self.yolo_times else 0,
                'fps': len(self.yolo_times) / self.total_yolo_time if self.total_yolo_time > 0 else 0
            },
            'frame_processing': {
                'total_frames': len(self.frame_processing_times),
                'average_time': sum(self.frame_processing_times) / len(self.frame_processing_times) if self.frame_processing_times else 0,
                'min_time': min(self.frame_processing_times) if self.frame_processing_times else 0,
                'max_time': max(self.frame_processing_times) if self.frame_processing_times else 0,
                'fps': len(self.frame_processing_times) / sum(self.frame_processing_times) if sum(self.frame_processing_times) > 0 else 0
            }
        }

        # Calculate percentiles for inference times
        if self.inference_times:
            sorted_times = sorted(self.inference_times)
            n = len(sorted_times)
            stats['inference']['p50'] = sorted_times[n//2]
            stats['inference']['p95'] = sorted_times[int(n*0.95)]
            stats['inference']['p99'] = sorted_times[int(n*0.99)]

        # Calculate percentiles for YOLO times
        if self.yolo_times:
            sorted_times = sorted(self.yolo_times)
            n = len(sorted_times)
            stats['yolo']['p50'] = sorted_times[n//2]
            stats['yolo']['p95'] = sorted_times[int(n*0.95)]
            stats['yolo']['p99'] = sorted_times[int(n*0.99)]

        return stats

    def _print_performance_stats(self, stats: Dict, total_processing_time: float):
        """Print detailed performance statistics"""
        print(f"\n🚀 Performance Statistics:")
        print(f"=" * 60)

        # Overall processing stats
        print(f"📊 Overall Processing:")
        print(f"   Total processing time: {total_processing_time:.2f}s")
        print(f"   Overall FPS: {self.frame_count / total_processing_time:.2f} frames/sec")

        # Model inference stats
        if stats['inference']['total_inferences'] > 0:
            print(f"\n🧠 Model Inference Performance:")
            print(f"   Total inferences: {stats['inference']['total_inferences']}")
            print(f"   Total inference time: {stats['inference']['total_time']:.2f}s")
            print(f"   Average inference time: {stats['inference']['average_time']*1000:.1f}ms")
            print(f"   Min inference time: {stats['inference']['min_time']*1000:.1f}ms")
            print(f"   Max inference time: {stats['inference']['max_time']*1000:.1f}ms")
            print(f"   Inference FPS: {stats['inference']['fps']:.2f} inferences/sec")

            if 'p50' in stats['inference']:
                print(f"   Median (P50): {stats['inference']['p50']*1000:.1f}ms")
                print(f"   P95: {stats['inference']['p95']*1000:.1f}ms")
                print(f"   P99: {stats['inference']['p99']*1000:.1f}ms")

        # YOLO detection stats
        if stats['yolo']['total_detections'] > 0:
            print(f"\n👁️  YOLO Detection Performance:")
            print(f"   Total detections: {stats['yolo']['total_detections']}")
            print(f"   Total detection time: {stats['yolo']['total_time']:.2f}s")
            print(f"   Average detection time: {stats['yolo']['average_time']*1000:.1f}ms")
            print(f"   Min detection time: {stats['yolo']['min_time']*1000:.1f}ms")
            print(f"   Max detection time: {stats['yolo']['max_time']*1000:.1f}ms")
            print(f"   Detection FPS: {stats['yolo']['fps']:.2f} detections/sec")

            if 'p50' in stats['yolo']:
                print(f"   Median (P50): {stats['yolo']['p50']*1000:.1f}ms")
                print(f"   P95: {stats['yolo']['p95']*1000:.1f}ms")
                print(f"   P99: {stats['yolo']['p99']*1000:.1f}ms")

        # Frame processing stats
        if stats['frame_processing']['total_frames'] > 0:
            print(f"\n🎬 Frame Processing Performance:")
            print(f"   Total frames processed: {stats['frame_processing']['total_frames']}")
            print(f"   Average frame processing time: {stats['frame_processing']['average_time']*1000:.1f}ms")
            print(f"   Min frame processing time: {stats['frame_processing']['min_time']*1000:.1f}ms")
            print(f"   Max frame processing time: {stats['frame_processing']['max_time']*1000:.1f}ms")
            print(f"   Frame processing FPS: {stats['frame_processing']['fps']:.2f} frames/sec")

        # Performance breakdown
        if stats['inference']['total_time'] > 0 and stats['yolo']['total_time'] > 0:
            total_ai_time = stats['inference']['total_time'] + stats['yolo']['total_time']
            inference_percentage = (stats['inference']['total_time'] / total_ai_time) * 100
            yolo_percentage = (stats['yolo']['total_time'] / total_ai_time) * 100

            print(f"\n⚡ AI Processing Breakdown:")
            print(f"   Model inference: {inference_percentage:.1f}% ({stats['inference']['total_time']:.2f}s)")
            print(f"   YOLO detection: {yolo_percentage:.1f}% ({stats['yolo']['total_time']:.2f}s)")
            print(f"   Total AI time: {total_ai_time:.2f}s ({(total_ai_time/total_processing_time)*100:.1f}% of total)")

        print(f"=" * 60)

    def _save_annotated_clip(self, frames: List, person_data: Dict, timestamp: str, frame_detections: List = None) -> str:
        """Save annotated video clip with bounding boxes and predictions"""
        annotated_filename = f"annotated_{timestamp}.mp4"
        annotated_path = os.path.join(self.annotated_dir, annotated_filename)

        h, w = frames[0].shape[:2]
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(annotated_path, fourcc, 16.0, (w, h))  # 16 FPS for individual clips

        # Annotate each frame
        for frame_idx, frame in enumerate(frames):
            annotated_frame = frame.copy()

            # Use frame-level detection data if available
            if frame_detections and frame_idx < len(frame_detections):
                frame_detection = frame_detections[frame_idx]
                boxes = frame_detection['boxes']
                confidences = frame_detection['confidences']
                ids = frame_detection['ids']

                # Draw bounding boxes for detected persons in this frame
                for box, conf, person_id in zip(boxes, confidences, ids):
                    if person_id in person_data:
                        prediction = person_data[person_id]['prediction']
                        x1, y1, x2, y2 = map(int, box)

                        # Choose color based on prediction
                        if prediction > 0.5:
                            color = (0, 0, 255)  # Red
                            if SHOW_PERSON_IDS:
                                label = f"Person {person_id}: SHOPLIFTING ({prediction:.2f})"
                            else:
                                label = f"SHOPLIFTING ({prediction:.2f})"
                        else:
                            color = (0, 255, 0)  # Green
                            if SHOW_PERSON_IDS:
                                label = f"Person {person_id}: NORMAL ({prediction:.2f})"
                            else:
                                label = f"NORMAL ({prediction:.2f})"

                        # Draw bounding box
                        cv2.rectangle(annotated_frame, (x1, y1), (x2, y2), color, 2)

                        # Draw label
                        label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
                        cv2.rectangle(annotated_frame, (x1, y1 - label_size[1] - 10),
                                     (x1 + label_size[0], y1), color, -1)
                        cv2.putText(annotated_frame, label, (x1, y1 - 5),
                                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

                        # Draw confidence
                        conf_text = f"Conf: {conf:.2f}"
                        cv2.putText(annotated_frame, conf_text, (x1, y2 + 20),
                                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)

            # Add timestamp
            timestamp_text = f"Time: {timestamp}"
            cv2.putText(annotated_frame, timestamp_text, (10, 30),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

            out.write(annotated_frame)

        out.release()
        return annotated_path

    def combine_results(self) -> Optional[str]:
        """Combine all annotated clips into a single video"""
        try:
            from combine_session_clips import combine_session_clips

            print(f"\n🎬 Combining annotated clips...")
            success = combine_session_clips(self.session_dir, self.session_dir)

            if success:
                video_name = Path(self.video_path).stem
                combined_filename = f"combined_{video_name}_{self.session_timestamp}.mp4"
                combined_path = os.path.join(self.session_dir, combined_filename)
                print(f"✅ Clips combined: {combined_path}")
                return combined_path
            else:
                print("❌ Failed to combine clips")
                return None

        except Exception as e:
            print(f"❌ Error combining clips: {e}")
            return None

    def get_summary(self) -> Dict:
        """Get processing summary with statistics"""
        suspicious_results = [r for r in self.all_results if r['is_suspicious']]
        normal_results = [r for r in self.all_results if not r['is_suspicious']]

        return {
            'total_predictions': len(self.all_results),
            'suspicious_detections': len(suspicious_results),
            'normal_detections': len(normal_results),
            'suspicious_rate': len(suspicious_results) / len(self.all_results) * 100 if self.all_results else 0,
            'clips_processed': self.clip_count,
            'persons_detected': self.person_count,
            'session_directory': self.session_dir,
            'suspicious_results': suspicious_results,
            'normal_results': normal_results
        }


def quick_process(video_path: str = None, show_realtime: bool = True, playback_speed: float = 1.0):
    """
    Quick and easy video processing function

    Args:
        video_path: Path to video file (if None, uses DEFAULT_VIDEO_PATH)
        show_realtime: Whether to show real-time display
        playback_speed: Playback speed (1.0 = normal)
    """
    if video_path is None:
        video_path = DEFAULT_VIDEO_PATH

    print("🎥 Quick Video Processing")
    print("=" * 40)
    print(f"📹 Video: {video_path}")
    print(f"📺 Real-time display: {'Enabled' if show_realtime else 'Disabled'}")
    if show_realtime:
        print(f"⚡ Speed: {playback_speed}x")
        print(f"🎮 Controls: 'q'=quit, 'p'=pause, 's'=screenshot")
    print("=" * 40)

    # Check if video exists
    if not os.path.exists(video_path):
        print(f"❌ Error: Video file not found: {video_path}")
        print(f"💡 Please update DEFAULT_VIDEO_PATH at the top of this file")
        return None

    try:
        # Create processor
        processor = VideoFileProcessor(
            video_path=video_path,
            output_dir="data/output/quick_processing"
        )

        # Process video
        results = processor.process_video(
            show_realtime=show_realtime,
            playback_speed=playback_speed
        )

        # Get summary
        summary = processor.get_summary()

        # Print results
        print(f"\n✅ Processing completed!")
        print(f"📊 Results:")
        print(f"   - Processing time: {results['processing_time']:.1f}s")
        print(f"   - Clips created: {results['clips_created']}")
        print(f"   - Predictions made: {summary['total_predictions']}")
        print(f"   - Suspicious detections: {summary['suspicious_detections']}")
        print(f"   - Session directory: {processor.session_dir}")

        # Print quick performance summary
        if 'performance_stats' in results:
            perf = results['performance_stats']
            print(f"⚡ Performance:")
            if perf['inference']['total_inferences'] > 0:
                print(f"   - Avg inference: {perf['inference']['average_time']*1000:.1f}ms")
            if perf['yolo']['total_detections'] > 0:
                print(f"   - Avg YOLO: {perf['yolo']['average_time']*1000:.1f}ms")
            print(f"   - Overall FPS: {results['fps_processed']:.1f}")

        # Combine clips
        combined_path = processor.combine_results()
        if combined_path:
            print(f"   - Combined video: {combined_path}")

        return {
            'results': results,
            'summary': summary,
            'session_dir': processor.session_dir,
            'combined_video': combined_path
        }

    except Exception as e:
        print(f"❌ Error: {e}")
        return None


def main():
    """Main function with command-line interface"""
    parser = argparse.ArgumentParser(description='Process video file for shoplifting detection')
    parser.add_argument('video_path', nargs='?', default=DEFAULT_VIDEO_PATH,
                       help=f'Path to input video file (default: {DEFAULT_VIDEO_PATH})')
    parser.add_argument('--model', default='data/models/trained_model_cnn.pt',
                       help='Path to 3DCNN model')
    parser.add_argument('--yolo', default='data/models/yolo11s.pt',
                       help='Path to YOLO model')
    parser.add_argument('--output', default='data/output/video_processing',
                       help='Output directory')
    parser.add_argument('--frames', type=int, default=16,
                       help='Number of frames per clip')
    parser.add_argument('--confidence', type=float, default=0.1,
                       help='YOLO confidence threshold')
    parser.add_argument('--skip', type=int, default=0,
                       help='Number of frames to skip between processing (for speed)')
    parser.add_argument('--no-annotated', action='store_true',
                       help='Skip saving annotated clips (faster processing)')
    parser.add_argument('--no-combine', action='store_true',
                       help='Skip combining clips into final video')
    parser.add_argument('--quiet', action='store_true',
                       help='Reduce output verbosity')
    parser.add_argument('--no-realtime', action='store_true',
                       help='Disable real-time video display')
    parser.add_argument('--speed', type=float, default=1.0,
                       help='Playback speed for real-time display (1.0 = normal speed)')

    args = parser.parse_args()

    # Validate input file
    if not os.path.exists(args.video_path):
        print(f"❌ Error: Video file not found: {args.video_path}")
        return 1

    print("=" * 70)
    print("🎥 Video File Processor for Shoplifting Detection")
    print("=" * 70)
    print(f"📹 Input video: {args.video_path}")
    print(f"🤖 Models: 3DCNN ({args.model}), YOLO ({args.yolo})")
    print(f"📁 Output directory: {args.output}")
    print(f"⚙️  Settings: {args.frames} frames/clip, confidence: {args.confidence}")
    if args.skip > 0:
        print(f"⏩ Frame skipping: {args.skip} frames (faster processing)")
    if not args.no_realtime:
        print(f"📺 Real-time display: Enabled ({args.speed}x speed)")
        print(f"🎮 Controls: 'q'=quit, 'p'=pause, 's'=screenshot")
    else:
        print(f"📺 Real-time display: Disabled")
    print("=" * 70)

    try:
        # Initialize processor
        processor = VideoFileProcessor(
            video_path=args.video_path,
            model_path=args.model,
            yolo_path=args.yolo,
            output_dir=args.output,
            frame_buffer_size=args.frames,
            confidence_threshold=args.confidence,
            skip_frames=args.skip
        )

        # Process video
        results = processor.process_video(
            display_progress=not args.quiet,
            save_annotated=not args.no_annotated,
            show_realtime=not args.no_realtime,
            playback_speed=args.speed
        )

        # Get summary
        summary = processor.get_summary()

        # Print results
        print(f"\n📊 Processing Summary:")
        print(f"   📹 Video: {Path(args.video_path).name}")
        print(f"   ⏱️  Processing time: {results['processing_time']:.1f}s")
        print(f"   📊 Frames: {results['frames_processed']} processed at {results['fps_processed']:.1f} FPS")
        print(f"   🎬 Clips: {results['clips_created']} created")
        print(f"   👥 Persons: {summary['persons_detected']} detected")
        print(f"   🧠 Predictions: {summary['total_predictions']} made")
        print(f"   🚨 Suspicious: {summary['suspicious_detections']} ({summary['suspicious_rate']:.1f}%)")
        print(f"   ✅ Normal: {summary['normal_detections']}")

        # Print performance summary
        if 'performance_stats' in results:
            perf = results['performance_stats']
            print(f"\n⚡ Performance Summary:")
            if perf['inference']['total_inferences'] > 0:
                print(f"   🧠 Avg inference time: {perf['inference']['average_time']*1000:.1f}ms ({perf['inference']['fps']:.1f} FPS)")
            if perf['yolo']['total_detections'] > 0:
                print(f"   👁️  Avg YOLO time: {perf['yolo']['average_time']*1000:.1f}ms ({perf['yolo']['fps']:.1f} FPS)")
            if perf['frame_processing']['total_frames'] > 0:
                print(f"   🎬 Avg frame processing: {perf['frame_processing']['average_time']*1000:.1f}ms")

        # Show suspicious detections
        if summary['suspicious_results']:
            print(f"\n🚨 Suspicious Detections:")
            for result in summary['suspicious_results']:
                print(f"   - Person {result['person_id']} in clip {result['timestamp']}: "
                      f"score {result['prediction']:.3f}")

        # Combine clips if requested
        if not args.no_combine and not args.no_annotated:
            combined_path = processor.combine_results()
            if combined_path:
                print(f"\n🎬 Final combined video: {combined_path}")

        print(f"\n📁 All results saved to: {processor.session_dir}")
        print("✅ Video processing completed successfully!")

        return 0

    except Exception as e:
        print(f"❌ Error processing video: {e}")
        import traceback
        traceback.print_exc()
        return 1


def process_video_simple(video_path: str, output_dir: str = None) -> Dict:
    """
    Simple function to process a video with default settings

    Args:
        video_path: Path to input video file
        output_dir: Output directory (optional)

    Returns:
        Dictionary with results and summary
    """
    if output_dir is None:
        output_dir = 'data/output/video_processing'

    processor = VideoFileProcessor(
        video_path=video_path,
        output_dir=output_dir
    )

    results = processor.process_video()
    summary = processor.get_summary()
    combined_path = processor.combine_results()

    return {
        'results': results,
        'summary': summary,
        'combined_video': combined_path,
        'session_dir': processor.session_dir
    }


if __name__ == "__main__":
    # ========================================================================
    # 🚀 QUICK START OPTIONS:
    # ========================================================================

    # Option 1: Use command line arguments (default behavior)
    # Run: python video_processor.py
    # This will use DEFAULT_VIDEO_PATH specified at the top

    # Option 2: Quick processing with default settings
    # Uncomment the line below and comment out the main() call:
    # quick_process()

    # Option 3: Quick processing with custom settings
    # Uncomment and modify as needed:
    # quick_process(
    #     video_path="your_specific_video.mp4",  # Your video path
    #     show_realtime=True,                    # Real-time display
    #     playback_speed=1.0                     # Normal speed
    # )

    # Option 4: Multiple videos with different settings
    # quick_process("video1.mp4", show_realtime=True, playback_speed=1.0)
    # quick_process("video2.mp4", show_realtime=True, playback_speed=2.0)
    # quick_process("video3.mp4", show_realtime=False)  # Background processing

    # ========================================================================

    # Default: Use command line interface
    exit(main())
