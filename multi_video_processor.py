#!/usr/bin/env python3
"""
Multi-Video Batched Processor with Optimized Threading Architecture
- Thread 1: Video capture and clip generation (per video source)
- Thread 2: Batched YOLO detection and person cropping
- Thread 3: Batched CNN predictions on person crops
- Thread 4: Display and annotation of results
"""

import cv2
import os
import time
import threading
import queue
import numpy as np
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from collections import defaultdict
import torch
import math

# Import required modules
from utils.detection import detect_persons
from utils.cropping import crop_person
from utils.models import load_action_transformer
from utils.prediction import predict_from_crops

# ============================================================================
# 📹 CONFIGURATION:
# ============================================================================
INPUT_FOLDER = "data/feed"  # ← Change this to your video folder path
OUTPUT_DIR = "data/output/multi_batched_processing"
MODEL_PATH = 'data/models/trained_model_cnn.pt'
YOLO_PATH = 'data/models/yolo11s.pt'

# Display settings
MAX_WINDOW_WIDTH = 1280     # Grid window width in pixels
MAX_WINDOW_HEIGHT = 720     # Grid window height in pixels
GRID_PADDING = 2            # Pixels between video tiles
DISPLAY_FRAMERATE = 25      # Target framerate for grid display (FPS)
SHOW_PERSON_IDS = True      # Show person ID numbers in video display

# Processing settings
CLIP_FRAMES = 16
CONFIDENCE_THRESHOLD = 0.3
CROP_SIZE = (224, 224)
QUEUE_MAX_SIZE = 100
MAX_VIDEOS = 4              # Maximum number of videos to process simultaneously
BATCH_SIZE = 8              # Batch size for YOLO and CNN processing

# Model precision settings
USE_HALF_PRECISION = True   # Enable/disable half precision (FP16) mode
# ============================================================================


class PerformanceTracker:
    """Track performance statistics across all threads"""
    
    def __init__(self):
        self.lock = threading.Lock()
        self.start_time = time.time()
        self.stats = {
            'clips_generated': 0,
            'persons_detected': 0,
            'predictions_made': 0,
            'frames_displayed': 0,
            'yolo_batch_times': [],
            'cnn_batch_times': [],
            'display_times': []
        }
    
    def add_clips(self, count: int):
        with self.lock:
            self.stats['clips_generated'] += count
    
    def add_persons(self, count: int):
        with self.lock:
            self.stats['persons_detected'] += count
    
    def add_predictions(self, count: int):
        with self.lock:
            self.stats['predictions_made'] += count
    
    def add_yolo_batch_time(self, duration: float):
        with self.lock:
            self.stats['yolo_batch_times'].append(duration)
    
    def add_cnn_batch_time(self, duration: float):
        with self.lock:
            self.stats['cnn_batch_times'].append(duration)
    
    def add_display_time(self, duration: float):
        with self.lock:
            self.stats['frames_displayed'] += 1
            self.stats['display_times'].append(duration)
    
    def get_stats(self) -> Dict:
        with self.lock:
            total_time = time.time() - self.start_time
            
            def safe_avg(times_list):
                return sum(times_list) / len(times_list) if times_list else 0
            
            return {
                'total_time': total_time,
                'clips_generated': self.stats['clips_generated'],
                'persons_detected': self.stats['persons_detected'],
                'predictions_made': self.stats['predictions_made'],
                'frames_displayed': self.stats['frames_displayed'],
                'avg_yolo_batch_time': safe_avg(self.stats['yolo_batch_times']),
                'avg_cnn_batch_time': safe_avg(self.stats['cnn_batch_times']),
                'avg_display_time': safe_avg(self.stats['display_times']),
                'yolo_batches': len(self.stats['yolo_batch_times']),
                'cnn_batches': len(self.stats['cnn_batch_times']),
                'clip_fps': self.stats['clips_generated'] / total_time if total_time > 0 else 0,
                'prediction_fps': self.stats['predictions_made'] / total_time if total_time > 0 else 0,
                'display_fps': self.stats['frames_displayed'] / total_time if total_time > 0 else 0
            }


class MultiBatchedVideoProcessor:
    """Main processor with batched threading architecture for multiple videos"""
    
    def __init__(self, input_folder: str):
        self.input_folder = input_folder
        self.session_timestamp = time.strftime("%d-%m-%Y_%H-%M-%S")
        self.session_dir = os.path.join(OUTPUT_DIR, f"batched_session_{self.session_timestamp}")
        
        # Create directories
        os.makedirs(self.session_dir, exist_ok=True)
        
        # Get video files
        self.video_files = self._get_video_files()
        
        # Initialize queues
        self.video_clip_queues = {}  # Per video: raw clips
        self.person_queues = {}      # Per person: cropped sequences
        self.prediction_queue = queue.Queue(maxsize=QUEUE_MAX_SIZE)  # Annotated clips
        self.display_queue = queue.Queue(maxsize=QUEUE_MAX_SIZE)     # Final display
        
        # Initialize per-video clip queues
        for i in range(len(self.video_files)):
            self.video_clip_queues[i] = queue.Queue(maxsize=QUEUE_MAX_SIZE)
        
        # Control flags
        self.stop_event = threading.Event()
        self.videos_ended = set()
        
        # Performance tracking
        self.perf_tracker = PerformanceTracker()
        
        # Models (will be loaded in respective threads)
        self.device = None
        self.action_model = None
        self.yolo_model = None
        
        # Grid layout calculation
        self.grid_cols, self.grid_rows = self._calculate_grid_layout(len(self.video_files))
        self.tile_width, self.tile_height = self._calculate_tile_size()
        
        # Storage for final video combination
        self.annotated_clips = []
        
        print(f"📁 Input folder: {input_folder}")
        print(f"🎬 Found {len(self.video_files)} video files")
        print(f"📐 Grid layout: {self.grid_cols}x{self.grid_rows}")
        print(f"📁 Session directory: {self.session_dir}")
    
    def _get_video_files(self) -> List[str]:
        """Get list of video files from input folder"""
        if not os.path.exists(self.input_folder):
            raise FileNotFoundError(f"Input folder not found: {self.input_folder}")
        
        video_extensions = {'.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm'}
        video_files = []
        
        for file in os.listdir(self.input_folder):
            file_path = os.path.join(self.input_folder, file)
            if os.path.isfile(file_path):
                if Path(file).suffix.lower() in video_extensions:
                    video_files.append(file_path)
        
        if len(video_files) > MAX_VIDEOS:
            print(f"⚠️  Found {len(video_files)} videos, limiting to {MAX_VIDEOS}")
            video_files = video_files[:MAX_VIDEOS]
        
        return sorted(video_files)
    
    def _calculate_grid_layout(self, num_videos: int) -> Tuple[int, int]:
        """Calculate optimal grid layout for given number of videos"""
        if num_videos == 0:
            return 1, 1
        
        cols = math.ceil(math.sqrt(num_videos))
        rows = math.ceil(num_videos / cols)
        
        return cols, rows
    
    def _calculate_tile_size(self) -> Tuple[int, int]:
        """Calculate size of each video tile in the grid"""
        available_width = MAX_WINDOW_WIDTH - (self.grid_cols - 1) * GRID_PADDING
        available_height = MAX_WINDOW_HEIGHT - (self.grid_rows - 1) * GRID_PADDING
        
        tile_width = available_width // self.grid_cols
        tile_height = available_height // self.grid_rows
        
        # Ensure even dimensions
        tile_width = tile_width - (tile_width % 2)
        tile_height = tile_height - (tile_height % 2)
        
        return tile_width, tile_height

    def initialize_models(self):
        """Initialize CUDA models"""
        if not torch.cuda.is_available():
            raise RuntimeError("❌ CUDA is not available!")

        self.device = torch.device('cuda:0')
        torch.cuda.set_device(0)

        # Load CNN model
        self.action_model = load_action_transformer(MODEL_PATH, self.device)
        if self.action_model is None:
            raise RuntimeError("❌ Failed to load CNN model!")

        self.action_model = self.action_model.to(self.device)
        if USE_HALF_PRECISION:
            self.action_model = self.action_model.half()
        self.action_model.eval()

        if hasattr(self.action_model, 'backbone'):
            self.action_model.backbone.eval()

        # Load YOLO model
        from ultralytics import YOLO
        self.yolo_model = YOLO(YOLO_PATH)
        self.yolo_model.to(self.device)
        if USE_HALF_PRECISION:
            self.yolo_model.half()

        if hasattr(self.yolo_model, 'model'):
            self.yolo_model.model.eval()

        print("🔥 Models loaded and ready!")

    def video_capture_thread(self, video_index: int, video_path: str):
        """Thread 1: Capture video frames and create clips"""
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            print(f"❌ Could not open video {video_index}: {Path(video_path).name}")
            return

        frame_buffer = []
        frame_count = 0

        while not self.stop_event.is_set():
            ret, frame = cap.read()
            if not ret:
                self.videos_ended.add(video_index)
                break

            frame_count += 1
            frame_buffer.append(frame.copy())

            # Create clip when buffer is full
            if len(frame_buffer) == CLIP_FRAMES:
                clip_data = {
                    'video_index': video_index,
                    'frames': frame_buffer.copy(),
                    'timestamp': f"{video_index}_{frame_count//CLIP_FRAMES}",
                    'frame_range': (frame_count - CLIP_FRAMES + 1, frame_count)
                }

                try:
                    self.video_clip_queues[video_index].put(clip_data, timeout=0.1)
                    self.perf_tracker.add_clips(1)
                except queue.Full:
                    pass  # Drop clip if queue is full

                frame_buffer.clear()

        # Process remaining frames
        if len(frame_buffer) >= 8:  # Minimum frames for processing
            clip_data = {
                'video_index': video_index,
                'frames': frame_buffer.copy(),
                'timestamp': f"{video_index}_final",
                'frame_range': (frame_count - len(frame_buffer) + 1, frame_count)
            }

            try:
                self.video_clip_queues[video_index].put(clip_data, timeout=0.1)
                self.perf_tracker.add_clips(1)
            except queue.Full:
                pass

        cap.release()

    def batched_detection_thread(self):
        """Thread 2: Batched YOLO detection and person cropping"""
        batch_clips = []

        while not self.stop_event.is_set() or not all(q.empty() for q in self.video_clip_queues.values()):
            # Collect clips from all video sources for batching
            for video_index in range(len(self.video_files)):
                try:
                    clip_data = self.video_clip_queues[video_index].get_nowait()
                    batch_clips.append(clip_data)

                    if len(batch_clips) >= BATCH_SIZE:
                        self._process_detection_batch(batch_clips)
                        batch_clips.clear()

                except queue.Empty:
                    continue

            # Process remaining clips in batch
            if batch_clips and (len(self.videos_ended) == len(self.video_files)):
                self._process_detection_batch(batch_clips)
                batch_clips.clear()

            time.sleep(0.01)  # Small delay to prevent busy waiting

    def _process_detection_batch(self, batch_clips: List[Dict]):
        """Process a batch of clips for person detection"""
        start_time = time.time()

        for clip_data in batch_clips:
            person_crops = defaultdict(list)
            person_boxes = defaultdict(list)
            frame_detections = []

            # Process each frame in the clip
            for frame_idx, frame in enumerate(clip_data['frames']):
                try:
                    if hasattr(self.yolo_model, 'model'):
                        self.yolo_model.model.eval()

                    boxes, confidences, ids = detect_persons(self.yolo_model, frame, CONFIDENCE_THRESHOLD)
                    frame_detections.append({
                        'frame_idx': frame_idx,
                        'boxes': boxes,
                        'confidences': confidences,
                        'ids': ids
                    })

                    # Crop persons
                    for box, conf, person_id in zip(boxes, confidences, ids):
                        if conf > CONFIDENCE_THRESHOLD:
                            crop = crop_person(frame, box, CROP_SIZE)
                            if crop is not None:
                                person_crops[person_id].append(crop)
                                person_boxes[person_id].append(box)

                except Exception:
                    frame_detections.append({
                        'frame_idx': frame_idx,
                        'boxes': [], 'confidences': [], 'ids': []
                    })

            # Create person queues and add data
            for person_id, crops in person_crops.items():
                if len(crops) >= 8:  # Minimum frames for prediction
                    person_key = f"{clip_data['video_index']}_{person_id}"

                    if person_key not in self.person_queues:
                        self.person_queues[person_key] = queue.Queue(maxsize=QUEUE_MAX_SIZE)

                    person_data = {
                        'person_key': person_key,
                        'video_index': clip_data['video_index'],
                        'person_id': person_id,
                        'crops': crops,
                        'boxes': person_boxes[person_id],
                        'original_clip': clip_data,
                        'frame_detections': frame_detections
                    }

                    try:
                        self.person_queues[person_key].put(person_data, timeout=0.1)
                        self.perf_tracker.add_persons(1)
                    except queue.Full:
                        pass

        batch_time = time.time() - start_time
        self.perf_tracker.add_yolo_batch_time(batch_time)

    def batched_prediction_thread(self):
        """Thread 3: Batched CNN predictions on person crops"""
        batch_persons = []

        while not self.stop_event.is_set() or any(not q.empty() for q in self.person_queues.values()):
            # Collect person data from all person queues for batching
            for person_key, person_queue in list(self.person_queues.items()):
                try:
                    person_data = person_queue.get_nowait()
                    batch_persons.append(person_data)

                    if len(batch_persons) >= BATCH_SIZE:
                        self._process_prediction_batch(batch_persons)
                        batch_persons.clear()

                except queue.Empty:
                    continue

            # Process remaining persons in batch
            if batch_persons and (len(self.videos_ended) == len(self.video_files)):
                self._process_prediction_batch(batch_persons)
                batch_persons.clear()

            time.sleep(0.01)  # Small delay to prevent busy waiting

    def _process_prediction_batch(self, batch_persons: List[Dict]):
        """Process a batch of person crops for CNN prediction"""
        start_time = time.time()

        for person_data in batch_persons:
            try:
                # Ensure models are in eval mode
                self.action_model.eval()
                if hasattr(self.action_model, 'backbone'):
                    self.action_model.backbone.eval()

                # Make prediction
                prediction = predict_from_crops(self.action_model, person_data['crops'], self.device)

                # Create annotated clip data
                annotated_data = {
                    'video_index': person_data['video_index'],
                    'person_id': person_data['person_id'],
                    'prediction': prediction,
                    'original_clip': person_data['original_clip'],
                    'frame_detections': person_data['frame_detections'],
                    'person_boxes': person_data['boxes']
                }

                try:
                    self.prediction_queue.put(annotated_data, timeout=0.1)
                    self.perf_tracker.add_predictions(1)

                    # Print only suspicious behavior
                    if prediction > 0.5:
                        video_name = Path(self.video_files[person_data['video_index']]).stem
                        if SHOW_PERSON_IDS:
                            print(f"🚨 SUSPICIOUS - {video_name} Person {person_data['person_id']}: {prediction:.3f}")
                        else:
                            print(f"🚨 SUSPICIOUS - {video_name}: {prediction:.3f}")

                except queue.Full:
                    pass

            except Exception:
                pass  # Skip failed predictions

        batch_time = time.time() - start_time
        self.perf_tracker.add_cnn_batch_time(batch_time)

    def display_thread(self):
        """Thread 4: Display annotated video grid"""
        cv2.namedWindow('Multi-Video Batched Processor', cv2.WINDOW_NORMAL)
        cv2.resizeWindow('Multi-Video Batched Processor', MAX_WINDOW_WIDTH, MAX_WINDOW_HEIGHT)

        frame_delay = int(1000 / DISPLAY_FRAMERATE)
        current_frames = {}
        current_annotations = defaultdict(dict)

        while not self.stop_event.is_set():
            start_time = time.time()

            # Process annotation data
            try:
                while True:
                    annotated_data = self.prediction_queue.get_nowait()
                    video_index = annotated_data['video_index']
                    person_id = annotated_data['person_id']

                    # Store annotation for this video/person
                    current_annotations[video_index][person_id] = annotated_data

                    # Create annotated frames
                    annotated_frames = []
                    for frame_idx, frame in enumerate(annotated_data['original_clip']['frames']):
                        annotated_frame = frame.copy()

                        # Get detections for this frame
                        frame_detection = annotated_data['frame_detections'][frame_idx]
                        boxes = frame_detection['boxes']
                        ids = frame_detection['ids']

                        # Draw bounding boxes for this person
                        for box, det_id in zip(boxes, ids):
                            if det_id == person_id:
                                prediction_score = annotated_data['prediction']
                                x1, y1, x2, y2 = map(int, box)

                                # Choose color based on prediction
                                if prediction_score > 0.5:
                                    color = (0, 0, 255)  # Red for suspicious
                                    if SHOW_PERSON_IDS:
                                        label = f"Person {person_id}: SUSPICIOUS ({prediction_score:.2f})"
                                    else:
                                        label = f"SUSPICIOUS ({prediction_score:.2f})"
                                else:
                                    color = (0, 255, 0)  # Green for normal
                                    if SHOW_PERSON_IDS:
                                        label = f"Person {person_id}: NORMAL ({prediction_score:.2f})"
                                    else:
                                        label = f"NORMAL ({prediction_score:.2f})"

                                # Draw bounding box
                                cv2.rectangle(annotated_frame, (x1, y1), (x2, y2), color, 2)

                                # Draw label
                                label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
                                cv2.rectangle(annotated_frame, (x1, y1 - label_size[1] - 10),
                                            (x1 + label_size[0], y1), color, -1)
                                cv2.putText(annotated_frame, label, (x1, y1 - 5),
                                          cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

                        annotated_frames.append(annotated_frame)

                    # Store annotated clip for saving
                    self.annotated_clips.append({
                        'video_index': video_index,
                        'frames': annotated_frames,
                        'timestamp': annotated_data['original_clip']['timestamp']
                    })

                    # Use last frame for grid display
                    if annotated_frames:
                        resized_frame = cv2.resize(annotated_frames[-1], (self.tile_width, self.tile_height))
                        current_frames[video_index] = resized_frame

            except queue.Empty:
                pass

            # Create and display grid
            grid_frame = self._create_grid_display(current_frames)
            cv2.imshow('Multi-Video Batched Processor', grid_frame)

            # Handle keyboard input
            key = cv2.waitKey(frame_delay) & 0xFF
            if key == ord('q'):
                print("🛑 User requested quit")
                self.stop_event.set()
                break

            # Check if all videos ended and queues are empty
            if (len(self.videos_ended) == len(self.video_files) and
                all(q.empty() for q in self.video_clip_queues.values()) and
                all(q.empty() for q in self.person_queues.values()) and
                self.prediction_queue.empty()):
                print("🏁 All processing completed")
                time.sleep(2)
                self.stop_event.set()
                break

            display_time = time.time() - start_time
            self.perf_tracker.add_display_time(display_time)

        cv2.destroyAllWindows()

    def _create_grid_display(self, current_frames: Dict[int, np.ndarray]) -> np.ndarray:
        """Create grid display from current frames"""
        grid_frame = np.zeros((MAX_WINDOW_HEIGHT, MAX_WINDOW_WIDTH, 3), dtype=np.uint8)

        for video_index, frame in current_frames.items():
            row = video_index // self.grid_cols
            col = video_index % self.grid_cols

            x = col * (self.tile_width + GRID_PADDING)
            y = row * (self.tile_height + GRID_PADDING)

            if x + self.tile_width <= MAX_WINDOW_WIDTH and y + self.tile_height <= MAX_WINDOW_HEIGHT:
                grid_frame[y:y+self.tile_height, x:x+self.tile_width] = frame

                # Add video label
                video_name = Path(self.video_files[video_index]).stem
                cv2.putText(grid_frame, f"Video {video_index + 1}: {video_name}", (x + 10, y + 30),
                          cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)

        return grid_frame

    def save_annotated_clips(self):
        """Save all annotated clips after session ends"""
        if not self.annotated_clips:
            return

        print(f"💾 Saving {len(self.annotated_clips)} annotated clips...")

        # Group clips by video
        clips_by_video = defaultdict(list)
        for clip in self.annotated_clips:
            clips_by_video[clip['video_index']].append(clip)

        # Save clips for each video
        for video_index, clips in clips_by_video.items():
            video_name = Path(self.video_files[video_index]).stem
            video_dir = os.path.join(self.session_dir, f"video_{video_index}_{video_name}")
            os.makedirs(video_dir, exist_ok=True)

            # Combine all clips for this video
            all_frames = []
            for clip in sorted(clips, key=lambda x: x['timestamp']):
                all_frames.extend(clip['frames'])

            if all_frames:
                # Save combined video
                output_path = os.path.join(video_dir, f"annotated_{video_name}.mp4")
                height, width = all_frames[0].shape[:2]
                fourcc = cv2.VideoWriter_fourcc(*'mp4v')
                out = cv2.VideoWriter(output_path, fourcc, 10.0, (width, height))

                for frame in all_frames:
                    out.write(frame)

                out.release()

        print(f"✅ Annotated clips saved to: {self.session_dir}")

    def process_videos(self):
        """Main processing method - starts all threads and manages execution"""
        print("🚀 Starting multi-video batched processing...")
        print(f"📹 Videos: {len(self.video_files)} files")
        print(f"📺 Display: {MAX_WINDOW_WIDTH}x{MAX_WINDOW_HEIGHT} grid")
        print(f"⚡ Precision: {'Half (FP16)' if USE_HALF_PRECISION else 'Full (FP32)'}")
        print(f"🔄 Batch size: {BATCH_SIZE}")
        print("=" * 60)

        # Initialize models
        self.initialize_models()

        # Create and start threads
        threads = []

        # Video capture threads (one per video)
        for i, video_path in enumerate(self.video_files):
            thread = threading.Thread(target=self.video_capture_thread, args=(i, video_path), name=f"Capture-{i}")
            threads.append(thread)

        # Processing threads
        detection_thread = threading.Thread(target=self.batched_detection_thread, name="BatchedDetection")
        prediction_thread = threading.Thread(target=self.batched_prediction_thread, name="BatchedPrediction")
        display_thread = threading.Thread(target=self.display_thread, name="Display")

        threads.extend([detection_thread, prediction_thread, display_thread])

        # Start all threads
        for thread in threads:
            thread.daemon = True
            thread.start()

        try:
            # Wait for all threads to complete
            for thread in threads:
                thread.join()
        except KeyboardInterrupt:
            print("\n🛑 Keyboard interrupt received")
            self.stop_event.set()
            time.sleep(2)

        # Save annotated clips
        self.save_annotated_clips()

        # Print final statistics
        self._print_final_stats()

        return {
            'session_dir': self.session_dir,
            'performance_stats': self.perf_tracker.get_stats()
        }

    def _print_final_stats(self):
        """Print comprehensive final performance statistics"""
        stats = self.perf_tracker.get_stats()

        print(f"\n🏁 Final Performance Statistics:")
        print("=" * 60)
        print(f"📊 Processing Summary:")
        print(f"   Total runtime: {stats['total_time']:.2f}s")
        print(f"   Videos processed: {len(self.video_files)}")
        print(f"   Clips generated: {stats['clips_generated']}")
        print(f"   Persons detected: {stats['persons_detected']}")
        print(f"   Predictions made: {stats['predictions_made']}")
        print(f"   Frames displayed: {stats['frames_displayed']}")

        print(f"\n⚡ Batched Performance:")
        print(f"   YOLO batches: {stats['yolo_batches']}")
        print(f"   CNN batches: {stats['cnn_batches']}")
        print(f"   Avg YOLO batch time: {stats['avg_yolo_batch_time']*1000:.1f}ms")
        print(f"   Avg CNN batch time: {stats['avg_cnn_batch_time']*1000:.1f}ms")
        print(f"   Avg display time: {stats['avg_display_time']*1000:.1f}ms")

        print(f"\n📈 Throughput:")
        print(f"   Clip generation: {stats['clip_fps']:.2f} clips/s")
        print(f"   Prediction rate: {stats['prediction_fps']:.2f} predictions/s")
        print(f"   Display FPS: {stats['display_fps']:.2f} frames/s")

        print(f"\n📁 Output:")
        print(f"   Annotated clips: {len(self.annotated_clips)}")
        print(f"   Session directory: {self.session_dir}")
        print("=" * 60)


def main():
    """Main function to run multi-video batched processor"""
    try:
        if not os.path.exists(INPUT_FOLDER):
            print(f"❌ Input folder not found: {INPUT_FOLDER}")
            return 1

        processor = MultiBatchedVideoProcessor(INPUT_FOLDER)

        # Process videos
        results = processor.process_videos()

        print(f"\n✅ Multi-video batched processing completed!")
        print(f"📁 Session directory: {results['session_dir']}")

        return 0

    except Exception as e:
        print(f"❌ Error: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
