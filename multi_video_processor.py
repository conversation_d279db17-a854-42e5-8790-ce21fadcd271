#!/usr/bin/env python3
"""
Multi-Video Threaded Processor with Queue-based Architecture
Processes multiple videos using separate threads for clip generation, person detection, prediction, and display
"""

import cv2
import os
import time
import threading
import queue
import numpy as np
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from collections import defaultdict
import torch
import math

# Import required modules
from utils.detection import detect_persons
from utils.cropping import crop_person
from utils.models import load_action_transformer
from utils.prediction import predict_with_timing

# ============================================================================
# 📹 CONFIGURATION:
# ============================================================================
INPUT_FOLDER = "data/feed"  # ← Change this to your video folder path
OUTPUT_DIR = "data/output/multi_threaded_processing"
MODEL_PATH = 'data/models/trained_model_cnn.pt'
YOLO_PATH = 'data/models/yolo11s.pt'

# Display settings
MAX_WINDOW_WIDTH = 1280     # Grid window width in pixels
MAX_WINDOW_HEIGHT = 720     # Grid window height in pixels
GRID_PADDING = 2            # Pixels between video tiles
DISPLAY_FRAMERATE = 16      # Target framerate for grid display (FPS)
SHOW_PERSON_IDS = True      # Show person ID numbers in video display

# Processing settings
CLIP_FRAMES = 16
CONFIDENCE_THRESHOLD = 0.3
CROP_SIZE = (224, 224)
QUEUE_MAX_SIZE = 50
MAX_VIDEOS = 4              # Maximum number of videos to process simultaneously

# Model precision settings
USE_HALF_PRECISION = False   # Enable/disable half precision (FP16) mode

# Verbosity settings
VERBOSE_MODE = False         # Enable/disable verbose output during processing
# ============================================================================


class PerformanceTracker:
    """Track performance statistics across all threads"""
    
    def __init__(self):
        self.lock = threading.Lock()
        self.stats = {
            'clips_generated': 0,
            'persons_detected': 0,
            'predictions_made': 0,
            'frames_displayed': 0,
            'clip_generation_times': [],
            'detection_times': [],
            'prediction_times': [],
            'display_times': [],
            'start_time': time.time()
        }
    
    def add_clip_time(self, duration: float):
        with self.lock:
            self.stats['clips_generated'] += 1
            self.stats['clip_generation_times'].append(duration)
    
    def add_detection_time(self, duration: float, persons_count: int):
        with self.lock:
            self.stats['persons_detected'] += persons_count
            self.stats['detection_times'].append(duration)
    
    def add_prediction_time(self, duration: float):
        with self.lock:
            self.stats['predictions_made'] += 1
            self.stats['prediction_times'].append(duration)
    
    def add_display_time(self, duration: float):
        with self.lock:
            self.stats['frames_displayed'] += 1
            self.stats['display_times'].append(duration)
    
    def get_stats(self) -> Dict:
        with self.lock:
            total_time = time.time() - self.stats['start_time']
            
            def safe_avg(times_list):
                return sum(times_list) / len(times_list) if times_list else 0
            
            return {
                'total_time': total_time,
                'clips_generated': self.stats['clips_generated'],
                'persons_detected': self.stats['persons_detected'],
                'predictions_made': self.stats['predictions_made'],
                'frames_displayed': self.stats['frames_displayed'],
                'avg_clip_time': safe_avg(self.stats['clip_generation_times']),
                'avg_detection_time': safe_avg(self.stats['detection_times']),
                'avg_prediction_time': safe_avg(self.stats['prediction_times']),
                'avg_display_time': safe_avg(self.stats['display_times']),
                'clip_fps': self.stats['clips_generated'] / total_time if total_time > 0 else 0,
                'detection_fps': len(self.stats['detection_times']) / total_time if total_time > 0 else 0,
                'prediction_fps': self.stats['predictions_made'] / total_time if total_time > 0 else 0,
                'display_fps': self.stats['frames_displayed'] / total_time if total_time > 0 else 0
            }


class MultiVideoProcessor:
    """Main processor with queue-based multithreading architecture for multiple videos"""
    
    def __init__(self, input_folder: str):
        self.input_folder = input_folder
        self.session_timestamp = time.strftime("%d-%m-%Y_%H-%M-%S")
        self.session_dir = os.path.join(OUTPUT_DIR, f"multi_session_{self.session_timestamp}")
        
        # Create directories
        os.makedirs(self.session_dir, exist_ok=True)
        
        # Get video files
        self.video_files = self._get_video_files()
        
        # Initialize queues for each video
        self.video_queues = {}
        for i, video_path in enumerate(self.video_files):
            self.video_queues[i] = {
                'clip_queue': queue.Queue(maxsize=QUEUE_MAX_SIZE),
                'person_queue': queue.Queue(maxsize=QUEUE_MAX_SIZE),
                'prediction_queue': queue.Queue(maxsize=QUEUE_MAX_SIZE)
            }
        
        # Control flags
        self.stop_event = threading.Event()
        self.pause_event = threading.Event()
        self.videos_ended = {}  # Track which videos have ended
        
        # Performance tracking
        self.perf_tracker = PerformanceTracker()
        
        # Models (will be loaded in respective threads)
        self.device = None
        self.action_model = None
        self.yolo_model = None
        
        # Grid layout calculation
        self.grid_cols, self.grid_rows = self._calculate_grid_layout(len(self.video_files))
        self.tile_width, self.tile_height = self._calculate_tile_size()
        
        print(f"📁 Input folder: {input_folder}")
        print(f"🎬 Found {len(self.video_files)} video files")
        print(f"📐 Grid layout: {self.grid_cols}x{self.grid_rows}")
        print(f"📏 Tile size: {self.tile_width}x{self.tile_height}")
        print(f"📁 Session directory: {self.session_dir}")
    
    def _get_video_files(self) -> List[str]:
        """Get list of video files from input folder (excluding subfolders)"""
        if not os.path.exists(self.input_folder):
            raise FileNotFoundError(f"Input folder not found: {self.input_folder}")
        
        video_extensions = {'.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm'}
        video_files = []
        
        # Only get files from the main folder, not subfolders
        for file in os.listdir(self.input_folder):
            file_path = os.path.join(self.input_folder, file)
            if os.path.isfile(file_path):  # Ensure it's a file, not a directory
                if Path(file).suffix.lower() in video_extensions:
                    video_files.append(file_path)
        
        # Limit to max_videos
        if len(video_files) > MAX_VIDEOS:
            print(f"⚠️  Found {len(video_files)} videos, limiting to {MAX_VIDEOS}")
            video_files = video_files[:MAX_VIDEOS]
        
        return sorted(video_files)
    
    def _calculate_grid_layout(self, num_videos: int) -> Tuple[int, int]:
        """Calculate optimal grid layout for given number of videos"""
        if num_videos == 0:
            return 1, 1
        
        # Calculate grid dimensions
        cols = math.ceil(math.sqrt(num_videos))
        rows = math.ceil(num_videos / cols)
        
        return cols, rows
    
    def _calculate_tile_size(self) -> Tuple[int, int]:
        """Calculate size of each video tile in the grid"""
        # Account for padding between tiles
        available_width = MAX_WINDOW_WIDTH - (self.grid_cols - 1) * GRID_PADDING
        available_height = MAX_WINDOW_HEIGHT - (self.grid_rows - 1) * GRID_PADDING
        
        tile_width = available_width // self.grid_cols
        tile_height = available_height // self.grid_rows
        
        # Ensure even dimensions for video encoding compatibility
        tile_width = tile_width - (tile_width % 2)
        tile_height = tile_height - (tile_height % 2)
        
        return tile_width, tile_height

    def initialize_models(self):
        """Initialize CUDA-only models in half precision with evaluation mode"""
        # Force CUDA usage - fail if not available
        if not torch.cuda.is_available():
            raise RuntimeError("❌ CUDA is not available! This application requires a CUDA-capable GPU.")

        if torch.cuda.device_count() == 0:
            raise RuntimeError("❌ No CUDA devices found! This application requires a CUDA-capable GPU.")

        # Force CUDA device
        self.device = torch.device('cuda:0')
        torch.cuda.set_device(0)  # Explicitly set CUDA device

        if VERBOSE_MODE:
            print(f"🚀 Using device: {self.device} ({torch.cuda.get_device_name(0)})")
            print(f"🔥 CUDA Memory: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
            print(f"⚡ Half precision mode: {'ENABLED' if USE_HALF_PRECISION else 'DISABLED'}")

        # Load CNN model and force to CUDA with half precision
        if VERBOSE_MODE:
            print("Loading models...")
        self.action_model = load_action_transformer(MODEL_PATH, self.device)
        if self.action_model is None:
            raise RuntimeError("❌ Failed to load CNN model!")

        # Force model to CUDA and optionally half precision
        self.action_model = self.action_model.to(self.device)
        if USE_HALF_PRECISION:
            self.action_model = self.action_model.half()  # Convert to half precision
        self.action_model.eval()  # Set to evaluation mode

        # Set backbone to evaluation mode if it exists
        if hasattr(self.action_model, 'backbone'):
            self.action_model.backbone.eval()

        # Load YOLO model and force to CUDA with half precision
        from ultralytics import YOLO
        self.yolo_model = YOLO(YOLO_PATH)
        if self.yolo_model is None:
            raise RuntimeError("❌ Failed to load YOLO model!")

        # Force YOLO to CUDA and optionally half precision
        self.yolo_model.to(self.device)
        if USE_HALF_PRECISION:
            self.yolo_model.half()  # Convert to half precision

        # Set YOLO model to evaluation mode
        if hasattr(self.yolo_model, 'model'):
            self.yolo_model.model.eval()

        # Verify models are on CUDA and in correct precision
        if next(self.action_model.parameters()).device.type != 'cuda':
            raise RuntimeError("❌ CNN model is not on CUDA!")

        expected_dtype = torch.float16 if USE_HALF_PRECISION else torch.float32
        if next(self.action_model.parameters()).dtype != expected_dtype:
            raise RuntimeError(f"❌ CNN model precision mismatch!")

        # Perform CUDA verification test
        if VERBOSE_MODE:
            self._verify_cuda_setup()

        # Perform model and GPU warmup
        self._warmup_models()

    def _verify_cuda_setup(self):
        """Verify CUDA setup with current precision mode"""
        precision_mode = "half precision" if USE_HALF_PRECISION else "full precision"
        print(f"🔍 Verifying CUDA setup with {precision_mode}...")

        try:
            # Test CUDA tensor operations in current precision mode
            tensor_dtype = torch.float16 if USE_HALF_PRECISION else torch.float32
            test_tensor = torch.randn(1, 3, 16, 112, 112, device=self.device, dtype=tensor_dtype)

            # Test CNN model on CUDA with current precision
            with torch.no_grad():
                test_output = self.action_model(test_tensor)
                if test_output.device.type != 'cuda':
                    raise RuntimeError("❌ CNN model output is not on CUDA!")

                expected_dtype = torch.float16 if USE_HALF_PRECISION else torch.float32
                if test_output.dtype != expected_dtype:
                    raise RuntimeError(f"❌ CNN model output precision mismatch!")

            # Test YOLO model on CUDA
            test_frame = torch.randint(0, 255, (480, 640, 3), dtype=torch.uint8).cpu().numpy()
            _ = self.yolo_model(test_frame, verbose=False)  # Test YOLO inference

            print("✅ CUDA verification successful!")
            print(f"✅ {precision_mode.title()} inference working correctly!")

        except Exception as e:
            raise RuntimeError(f"❌ CUDA verification failed: {e}")

    def _warmup_models(self):
        """Warmup YOLO, CNN model, and GPU before processing starts"""
        try:
            # GPU warmup - allocate and deallocate memory to initialize GPU
            tensor_dtype = torch.float16 if USE_HALF_PRECISION else torch.float32
            warmup_tensor = torch.randn(1000, 1000, device=self.device, dtype=tensor_dtype)
            _ = torch.matmul(warmup_tensor, warmup_tensor.T)
            del warmup_tensor
            torch.cuda.empty_cache()

            # YOLO model warmup
            import numpy as np

            # Create multiple test frames for thorough warmup
            for i in range(3):
                warmup_frame = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)

                # Ensure YOLO is in eval mode
                if hasattr(self.yolo_model, 'model'):
                    self.yolo_model.model.eval()

                # Run YOLO inference
                _ = self.yolo_model(warmup_frame, verbose=False)

            # CNN model warmup
            # Ensure models are in eval mode
            self.action_model.eval()
            if hasattr(self.action_model, 'backbone'):
                self.action_model.backbone.eval()

            # Create test tensors for CNN warmup
            tensor_dtype = torch.float16 if USE_HALF_PRECISION else torch.float32
            for i in range(3):
                warmup_input = torch.randn(1, 3, 16, 112, 112, device=self.device, dtype=tensor_dtype)

                with torch.no_grad():
                    with torch.cuda.device(self.device):
                        _ = self.action_model(warmup_input)

                del warmup_input

            # Final memory cleanup
            torch.cuda.empty_cache()

            print("🔥 Models warmed up and ready for processing!")

        except Exception as e:
            if VERBOSE_MODE:
                print(f"⚠️  Warmup warning: {e}")
                print("🔄 Continuing with processing...")

    def clip_generation_thread(self, video_index: int, video_path: str):
        """Thread 1: Generate video clips and add to queue for specific video"""
        if VERBOSE_MODE:
            print(f"🎬 Starting clip generation thread for video {video_index}...")

        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            print(f"❌ Could not open video: {video_path}")
            return

        frame_buffer = []
        frame_count = 0

        while not self.stop_event.is_set():
            ret, frame = cap.read()
            if not ret:
                if VERBOSE_MODE:
                    print(f"📹 Video {video_index} ended - signaling completion")
                self.videos_ended[video_index] = True
                break

            frame_count += 1
            frame_buffer.append(frame.copy())

            # Process clip when buffer is full
            if len(frame_buffer) == CLIP_FRAMES:
                start_time = time.time()

                clip_data = {
                    'frames': frame_buffer.copy(),
                    'timestamp': f"{int(time.time() * 1000)}_{frame_count}",
                    'frame_start': frame_count - CLIP_FRAMES,
                    'frame_end': frame_count,
                    'video_index': video_index
                }

                try:
                    self.video_queues[video_index]['clip_queue'].put(clip_data, timeout=1.0)
                    clip_time = time.time() - start_time
                    self.perf_tracker.add_clip_time(clip_time)
                except queue.Full:
                    if VERBOSE_MODE:
                        print(f"⚠️  Clip queue full for video {video_index}, dropping clip")

                frame_buffer.clear()

        # Process any remaining frames in buffer when video ends
        if frame_buffer and len(frame_buffer) >= 4:  # Minimum frames for processing
            if VERBOSE_MODE:
                print(f"📦 Processing final clip for video {video_index} with {len(frame_buffer)} frames")
            start_time = time.time()

            clip_data = {
                'frames': frame_buffer.copy(),
                'timestamp': f"{int(time.time() * 1000)}_final",
                'frame_start': frame_count - len(frame_buffer),
                'frame_end': frame_count,
                'video_index': video_index
            }

            try:
                self.video_queues[video_index]['clip_queue'].put(clip_data, timeout=1.0)
                clip_time = time.time() - start_time
                self.perf_tracker.add_clip_time(clip_time)
            except queue.Full:
                if VERBOSE_MODE:
                    print(f"⚠️  Clip queue full for video {video_index}, dropping final clip")

        cap.release()

        if VERBOSE_MODE:
            print(f"✅ Clip generation thread for video {video_index} finished")

        time.sleep(1.0)

    def person_detection_thread(self, video_index: int):
        """Thread 2: Detect persons and crop from clips for specific video"""
        if VERBOSE_MODE:
            print(f"👁️  Starting person detection thread for video {video_index}...")

        while not self.stop_event.is_set():
            try:
                clip_data = self.video_queues[video_index]['clip_queue'].get(timeout=1.0)
            except queue.Empty:
                # Check if video has ended and queue is empty
                if video_index in self.videos_ended and self.video_queues[video_index]['clip_queue'].empty():
                    break
                continue

            start_time = time.time()

            # Process each frame in the clip
            person_crops = defaultdict(list)
            person_boxes = defaultdict(list)
            frame_detections = []

            for frame_idx, frame in enumerate(clip_data['frames']):
                try:
                    # Ensure YOLO model is in evaluation mode
                    if hasattr(self.yolo_model, 'model'):
                        self.yolo_model.model.eval()

                    boxes, confidences, ids = detect_persons(self.yolo_model, frame, CONFIDENCE_THRESHOLD)
                    frame_detections.append({
                        'frame_idx': frame_idx,
                        'boxes': boxes,
                        'confidences': confidences,
                        'ids': ids
                    })

                    # Crop persons
                    for box, conf, person_id in zip(boxes, confidences, ids):
                        if conf > CONFIDENCE_THRESHOLD:
                            crop = crop_person(frame, box, CROP_SIZE)
                            if crop is not None:
                                person_crops[person_id].append(crop)
                                person_boxes[person_id].append(box)

                except Exception as e:
                    # Ignore YOLO errors
                    if 'nms' not in str(e).lower() and VERBOSE_MODE:
                        print(f"⚠️  Detection warning in video {video_index}: {e}")
                    frame_detections.append({
                        'frame_idx': frame_idx,
                        'boxes': [], 'confidences': [], 'ids': []
                    })

            # Create person clip data
            person_data = {
                'original_clip': clip_data,
                'person_crops': dict(person_crops),
                'person_boxes': dict(person_boxes),
                'frame_detections': frame_detections,
                'timestamp': clip_data['timestamp'],
                'video_index': video_index
            }

            detection_time = time.time() - start_time
            self.perf_tracker.add_detection_time(detection_time, len(person_crops))

            try:
                self.video_queues[video_index]['person_queue'].put(person_data, timeout=1.0)
            except queue.Full:
                if VERBOSE_MODE:
                    print(f"⚠️  Person queue full for video {video_index}, dropping data")

            self.video_queues[video_index]['clip_queue'].task_done()

        if VERBOSE_MODE:
            print(f"✅ Person detection thread for video {video_index} finished")

    def prediction_thread(self, video_index: int):
        """Thread 3: Make predictions on person crops for specific video"""
        if VERBOSE_MODE:
            print(f"🧠 Starting prediction thread for video {video_index}...")

        while not self.stop_event.is_set():
            try:
                person_data = self.video_queues[video_index]['person_queue'].get(timeout=1.0)
            except queue.Empty:
                # Check if video has ended and queue is empty
                if video_index in self.videos_ended and self.video_queues[video_index]['person_queue'].empty():
                    break
                continue

            start_time = time.time()
            predictions = {}

            # Make predictions for each person
            for person_id, crops in person_data['person_crops'].items():
                if len(crops) >= 8:  # Minimum frames for prediction
                    try:
                        # Ensure models are in eval mode
                        self.action_model.eval()
                        if hasattr(self.action_model, 'backbone'):
                            self.action_model.backbone.eval()

                        # Make prediction using the crops directly
                        prediction = predict_with_timing(self.action_model, crops, self.device)
                        predictions[person_id] = {
                            'prediction': prediction,
                            'boxes': person_data['person_boxes'][person_id]
                        }

                        # Print result only for suspicious behavior or if verbose
                        if prediction > 0.5:  # Only print suspicious behavior
                            status = "🚨 SUSPICIOUS"
                            if SHOW_PERSON_IDS:
                                print(f"{status} - Video {video_index} Person {person_id}: {prediction:.3f}")
                            else:
                                print(f"{status} - Video {video_index} Detection: {prediction:.3f}")
                        elif VERBOSE_MODE:  # Print normal behavior only in verbose mode
                            status = "✅ NORMAL"
                            if SHOW_PERSON_IDS:
                                print(f"{status} - Video {video_index} Person {person_id}: {prediction:.3f}")
                            else:
                                print(f"{status} - Video {video_index} Detection: {prediction:.3f}")

                    except Exception as e:
                        if VERBOSE_MODE:
                            print(f"❌ Error predicting person {person_id} in video {video_index}: {e}")

            # Create prediction data
            prediction_data = {
                'original_clip': person_data['original_clip'],
                'frame_detections': person_data['frame_detections'],
                'predictions': predictions,
                'timestamp': person_data['timestamp'],
                'video_index': video_index
            }

            prediction_time = time.time() - start_time
            self.perf_tracker.add_prediction_time(prediction_time)

            try:
                self.video_queues[video_index]['prediction_queue'].put(prediction_data, timeout=1.0)
            except queue.Full:
                if VERBOSE_MODE:
                    print(f"⚠️  Prediction queue full for video {video_index}, dropping data")

            self.video_queues[video_index]['person_queue'].task_done()

        if VERBOSE_MODE:
            print(f"✅ Prediction thread for video {video_index} finished")

    def display_thread(self):
        """Thread 4: Display annotated video grid"""
        if VERBOSE_MODE:
            print("📺 Starting display thread for grid layout...")

        # Setup display window
        cv2.namedWindow('Multi-Video Processor Grid', cv2.WINDOW_NORMAL)
        cv2.resizeWindow('Multi-Video Processor Grid', MAX_WINDOW_WIDTH, MAX_WINDOW_HEIGHT)

        frame_delay = int(1000 / DISPLAY_FRAMERATE)
        current_frames = {}  # Store current frame for each video

        while not self.stop_event.is_set():
            start_time = time.time()

            # Check if all videos have ended
            all_ended = all(i in self.videos_ended for i in range(len(self.video_files)))
            if all_ended:
                # Check if all queues are empty
                all_empty = True
                for i in range(len(self.video_files)):
                    if not (self.video_queues[i]['clip_queue'].empty() and
                            self.video_queues[i]['person_queue'].empty() and
                            self.video_queues[i]['prediction_queue'].empty()):
                        all_empty = False
                        break

                if all_empty:
                    if VERBOSE_MODE:
                        print("🔍 All videos ended and queues empty")

                    # Double-check after another short wait
                    time.sleep(1.0)
                    all_empty = True
                    for i in range(len(self.video_files)):
                        if not (self.video_queues[i]['clip_queue'].empty() and
                                self.video_queues[i]['person_queue'].empty() and
                                self.video_queues[i]['prediction_queue'].empty()):
                            all_empty = False
                            break

                    if all_empty:
                        print("🏁 All processing completed - auto-quitting...")
                        time.sleep(2)
                        self.stop_event.set()
                        break

            # Process prediction data for each video
            for video_index in range(len(self.video_files)):
                try:
                    prediction_data = self.video_queues[video_index]['prediction_queue'].get_nowait()

                    # Create annotated frames
                    annotated_frames = []
                    for frame_idx, frame in enumerate(prediction_data['original_clip']['frames']):
                        annotated_frame = frame.copy()

                        # Get detections for this frame
                        frame_detection = prediction_data['frame_detections'][frame_idx]
                        boxes = frame_detection['boxes']
                        confidences = frame_detection['confidences']
                        ids = frame_detection['ids']
                        predictions = prediction_data['predictions']

                        # Draw bounding boxes
                        for box, _, person_id in zip(boxes, confidences, ids):
                            if person_id in predictions:
                                prediction_score = predictions[person_id]['prediction']
                                x1, y1, x2, y2 = map(int, box)

                                # Choose color based on prediction
                                if prediction_score > 0.5:
                                    color = (0, 0, 255)  # Red for suspicious
                                    if SHOW_PERSON_IDS:
                                        label = f"Person {person_id}: SUSPICIOUS ({prediction_score:.2f})"
                                    else:
                                        label = f"SUSPICIOUS ({prediction_score:.2f})"
                                else:
                                    color = (0, 255, 0)  # Green for normal
                                    if SHOW_PERSON_IDS:
                                        label = f"Person {person_id}: NORMAL ({prediction_score:.2f})"
                                    else:
                                        label = f"NORMAL ({prediction_score:.2f})"

                                # Draw bounding box
                                cv2.rectangle(annotated_frame, (x1, y1), (x2, y2), color, 2)

                                # Draw label background
                                label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
                                cv2.rectangle(annotated_frame, (x1, y1 - label_size[1] - 10),
                                            (x1 + label_size[0], y1), color, -1)

                                # Draw label text
                                cv2.putText(annotated_frame, label, (x1, y1 - 5),
                                          cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

                        annotated_frames.append(annotated_frame)

                    # Store the last frame for grid display
                    if annotated_frames:
                        # Resize frame to tile size
                        resized_frame = cv2.resize(annotated_frames[-1], (self.tile_width, self.tile_height))
                        current_frames[video_index] = resized_frame

                    self.video_queues[video_index]['prediction_queue'].task_done()

                except queue.Empty:
                    continue

            # Create grid display
            grid_frame = self._create_grid_display(current_frames)

            # Display the grid
            cv2.imshow('Multi-Video Processor Grid', grid_frame)

            # Handle keyboard input
            key = cv2.waitKey(frame_delay) & 0xFF
            if key == ord('q'):
                print("🛑 User requested quit")
                self.stop_event.set()
                break
            elif key == ord('p'):
                if self.pause_event.is_set():
                    self.pause_event.clear()
                    print("▶️ RESUMED")
                else:
                    self.pause_event.set()
                    print("⏸️ PAUSED")

            display_time = time.time() - start_time
            self.perf_tracker.add_display_time(display_time)

        cv2.destroyAllWindows()
        if VERBOSE_MODE:
            print("✅ Display thread finished")

    def _create_grid_display(self, current_frames: Dict[int, np.ndarray]) -> np.ndarray:
        """Create grid display from current frames"""
        # Create black background
        grid_frame = np.zeros((MAX_WINDOW_HEIGHT, MAX_WINDOW_WIDTH, 3), dtype=np.uint8)

        for video_index, frame in current_frames.items():
            # Calculate grid position
            row = video_index // self.grid_cols
            col = video_index % self.grid_cols

            # Calculate position in grid
            x = col * (self.tile_width + GRID_PADDING)
            y = row * (self.tile_height + GRID_PADDING)

            # Place frame in grid
            if x + self.tile_width <= MAX_WINDOW_WIDTH and y + self.tile_height <= MAX_WINDOW_HEIGHT:
                grid_frame[y:y+self.tile_height, x:x+self.tile_width] = frame

                # Add video index label
                label = f"Video {video_index + 1}"
                cv2.putText(grid_frame, label, (x + 10, y + 30),
                          cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

        return grid_frame

    def process_videos(self):
        """Main processing method - starts all threads and manages execution"""
        print("🚀 Starting multi-video threaded processing...")
        print(f"📹 Videos: {len(self.video_files)} files")
        print(f"📺 Display: {MAX_WINDOW_WIDTH}x{MAX_WINDOW_HEIGHT} grid")
        print(f"👥 Person IDs: {'Enabled' if SHOW_PERSON_IDS else 'Disabled'}")
        print(f"🚫 Frame Skipping: DISABLED - Processing every frame")
        print(f"⚡ Precision: {'Half (FP16)' if USE_HALF_PRECISION else 'Full (FP32)'}")
        print(f"📢 Verbose Mode: {'Enabled' if VERBOSE_MODE else 'Disabled'}")
        print(f"🏁 Auto-quit: ENABLED - Will quit automatically when all videos end")
        print(f"🎮 Controls: 'q'=quit, 'p'=pause")
        print("=" * 70)

        # Initialize models
        self.initialize_models()

        # Initialize videos_ended tracking
        self.videos_ended = {}

        # Create and start threads for each video
        threads = []

        # Create clip generation threads (one per video)
        for i, video_path in enumerate(self.video_files):
            clip_thread = threading.Thread(
                target=self.clip_generation_thread,
                args=(i, video_path),
                name=f"ClipGen-{i}"
            )
            threads.append(clip_thread)

        # Create person detection threads (one per video)
        for i in range(len(self.video_files)):
            detection_thread = threading.Thread(
                target=self.person_detection_thread,
                args=(i,),
                name=f"PersonDetect-{i}"
            )
            threads.append(detection_thread)

        # Create prediction threads (one per video)
        for i in range(len(self.video_files)):
            prediction_thread = threading.Thread(
                target=self.prediction_thread,
                args=(i,),
                name=f"Predict-{i}"
            )
            threads.append(prediction_thread)

        # Create single display thread for grid
        display_thread = threading.Thread(target=self.display_thread, name="Display")
        threads.append(display_thread)

        # Start all threads
        for thread in threads:
            thread.daemon = True
            thread.start()
            if VERBOSE_MODE:
                print(f"✅ Started {thread.name} thread")

        # Start performance monitoring
        self._start_performance_monitoring()

        try:
            # Wait for all threads to complete
            for thread in threads:
                thread.join()
        except KeyboardInterrupt:
            print("\n🛑 Keyboard interrupt received")
            self.stop_event.set()

            # Wait a bit for threads to stop gracefully
            time.sleep(2)

        # Print final statistics
        self._print_final_stats()

        print(f"\n✅ Multi-video processing completed successfully!")
        print(f"📁 Session directory: {self.session_dir}")

        return {
            'session_dir': self.session_dir,
            'performance_stats': self.perf_tracker.get_stats()
        }

    def _start_performance_monitoring(self):
        """Start performance monitoring thread"""
        def monitor_performance():
            while not self.stop_event.is_set():
                time.sleep(5.0)  # Update every 5 seconds
                if VERBOSE_MODE:
                    stats = self.perf_tracker.get_stats()

                    print(f"\n📊 Performance Update:")
                    print(f"   Clips: {stats['clips_generated']} ({stats['clip_fps']:.1f}/s)")
                    print(f"   Persons: {stats['persons_detected']} ({stats['detection_fps']:.1f}/s)")
                    print(f"   Predictions: {stats['predictions_made']} ({stats['prediction_fps']:.1f}/s)")
                    print(f"   Display: {stats['frames_displayed']} ({stats['display_fps']:.1f}/s)")
                    print(f"   Runtime: {stats['total_time']:.1f}s")

        monitor_thread = threading.Thread(target=monitor_performance, name="Monitor")
        monitor_thread.daemon = True
        monitor_thread.start()

    def _print_final_stats(self):
        """Print comprehensive final performance statistics"""
        stats = self.perf_tracker.get_stats()

        print(f"\n🏁 Final Performance Statistics:")
        print("=" * 70)
        print(f"📊 Processing Summary:")
        print(f"   Total runtime: {stats['total_time']:.2f}s")
        print(f"   Videos processed: {len(self.video_files)}")
        print(f"   Clips generated: {stats['clips_generated']}")
        print(f"   Persons detected: {stats['persons_detected']}")
        print(f"   Predictions made: {stats['predictions_made']}")
        print(f"   Frames displayed: {stats['frames_displayed']}")

        print(f"\n⚡ Performance Metrics:")
        print(f"   Average inference time: {stats['avg_prediction_time']*1000:.1f}ms")
        print(f"   Average YOLO detection time: {stats['avg_detection_time']*1000:.1f}ms")
        print(f"   Average prediction time: {stats['avg_prediction_time']*1000:.1f}ms")
        print(f"   Average throughput: {stats['prediction_fps']:.2f} predictions/s")
        print(f"   Overall FPS: {stats['frames_displayed'] / stats['total_time']:.2f} frames/s")

        print(f"\n📈 Detailed Timing:")
        print(f"   Clip generation: {stats['avg_clip_time']*1000:.1f}ms avg ({stats['clip_fps']:.2f} clips/s)")
        print(f"   Person detection: {stats['avg_detection_time']*1000:.1f}ms avg ({stats['detection_fps']:.2f} detections/s)")
        print(f"   CNN prediction: {stats['avg_prediction_time']*1000:.1f}ms avg ({stats['prediction_fps']:.2f} predictions/s)")
        print(f"   Display rendering: {stats['avg_display_time']*1000:.1f}ms avg ({stats['display_fps']:.2f} frames/s)")

        print(f"\n📁 Output:")
        print(f"   Session directory: {self.session_dir}")
        print("=" * 70)


def main():
    """Main function to run multi-video processor"""
    try:
        # Check if input folder exists
        if not os.path.exists(INPUT_FOLDER):
            print(f"❌ Input folder not found: {INPUT_FOLDER}")
            print("Please update INPUT_FOLDER in the script to point to your video directory")
            return 1
        
        # Create processor
        processor = MultiVideoProcessor(INPUT_FOLDER)

        # Process videos
        results = processor.process_videos()

        print(f"\n✅ Multi-video processing completed successfully!")
        print(f"📁 Session directory: {results['session_dir']}")

        return 0
        
    except KeyboardInterrupt:
        print("\n🛑 Processing interrupted by user")
        return 1
    except Exception as e:
        print(f"❌ Error during processing: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
