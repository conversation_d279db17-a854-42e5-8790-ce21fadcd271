#!/usr/bin/env python3
"""
Multi-Video Processor with Grid Display
Processes multiple videos in parallel and displays annotated output in a grid layout

Features:
- Processes multiple videos simultaneously using multithreading
- Displays all videos in a real-time grid layout within 720p resolution
- Shows person detection and shoplifting predictions with bounding boxes
- Automatically calculates optimal grid layout (2x2, 3x3, etc.)
- Optimized for performance with frame skipping and reduced processing load

Usage:
1. Set INPUT_FOLDER to your video directory path
2. Run the script: python multi_video_processor.py
3. Press 'q' to quit, 'p' to pause/resume

Requirements:
- All video files should be in the main folder (subfolders are excluded)
- Supports common video formats: .mp4, .avi, .mov, .mkv, .wmv, .flv, .webm
- Requires CUDA-capable GPU for optimal performance
"""

import os
import cv2
import time
import threading
import numpy as np
from pathlib import Path
from typing import List, Tuple
import math

# Import the VideoFileProcessor
from video_processor import VideoFileProcessor

# ============================================================================
# 📁 SPECIFY YOUR INPUT FOLDER HERE:
# ============================================================================
INPUT_FOLDER = "data/feed"  # ← Change this to your video folder path
# Examples:
# INPUT_FOLDER = "C:/Videos/security_footage"
# INPUT_FOLDER = "data/test_videos"
# INPUT_FOLDER = "sample_videos"
# ============================================================================

# Grid display settings
MAX_WINDOW_WIDTH = 1280  # 720p width
MAX_WINDOW_HEIGHT = 720  # 720p height
GRID_PADDING = 2  # Pixels between video tiles


class MultiVideoProcessor:
    """
    Process multiple videos in parallel and display them in a grid
    """
    
    def __init__(self, input_folder: str, max_videos: int = 4):
        """
        Initialize multi-video processor

        Args:
            input_folder: Folder containing video files
            max_videos: Maximum number of videos to process simultaneously
        """
        self.input_folder = input_folder
        self.max_videos = max_videos
        self.video_files = self._get_video_files()
        self.processors = {}
        self.current_frames = {}
        self.frame_locks = {}
        self.videos_finished = {}
        self.current_predictions = {}
        self.annotated_clips_memory = {}  # Store clips in memory
        self.running = False
        self.videos_finished = {}

        # Performance tracking across all threads
        self.thread_performance_stats = {}  # Store performance stats per thread
        self.session_start_time = None
        self.session_end_time = None
        
        # Calculate grid layout
        self.grid_cols, self.grid_rows = self._calculate_grid_layout(len(self.video_files))
        self.tile_width, self.tile_height = self._calculate_tile_size()
        
        print(f"📁 Input folder: {input_folder}")
        print(f"🎬 Found {len(self.video_files)} video files")
        print(f"📐 Grid layout: {self.grid_cols}x{self.grid_rows}")
        print(f"📏 Tile size: {self.tile_width}x{self.tile_height}")
        
    def _get_video_files(self) -> List[str]:
        """Get list of video files from input folder (excluding subfolders)"""
        if not os.path.exists(self.input_folder):
            raise FileNotFoundError(f"Input folder not found: {self.input_folder}")
        
        video_extensions = {'.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm'}
        video_files = []
        
        # Only get files from the main folder, not subfolders
        for file in os.listdir(self.input_folder):
            file_path = os.path.join(self.input_folder, file)
            if os.path.isfile(file_path):  # Ensure it's a file, not a directory
                if Path(file).suffix.lower() in video_extensions:
                    video_files.append(file_path)
        
        # Limit to max_videos
        if len(video_files) > self.max_videos:
            print(f"⚠️  Found {len(video_files)} videos, limiting to {self.max_videos}")
            video_files = video_files[:self.max_videos]
        
        return sorted(video_files)
    
    def _calculate_grid_layout(self, num_videos: int) -> Tuple[int, int]:
        """Calculate optimal grid layout for given number of videos"""
        if num_videos == 0:
            return 1, 1
        
        # Calculate grid dimensions
        cols = math.ceil(math.sqrt(num_videos))
        rows = math.ceil(num_videos / cols)
        
        return cols, rows
    
    def _calculate_tile_size(self) -> Tuple[int, int]:
        """Calculate size of each video tile in the grid"""
        # Account for padding between tiles
        available_width = MAX_WINDOW_WIDTH - (self.grid_cols - 1) * GRID_PADDING
        available_height = MAX_WINDOW_HEIGHT - (self.grid_rows - 1) * GRID_PADDING
        
        tile_width = available_width // self.grid_cols
        tile_height = available_height // self.grid_rows
        
        # Ensure even dimensions for video encoding compatibility
        tile_width = tile_width - (tile_width % 2)
        tile_height = tile_height - (tile_height % 2)
        
        return tile_width, tile_height

    def _process_clip_memory(self, processor, frames, timestamp, video_index):
        """Process a clip and store results in memory instead of saving immediately"""
        try:
            # Detect persons in all frames
            from collections import defaultdict
            person_crops = defaultdict(list)
            person_boxes = defaultdict(list)
            person_confidences = defaultdict(list)
            frame_detections = []

            for frame_idx, frame in enumerate(frames):
                try:
                    boxes, confidences, ids = processor._detect_persons(frame)
                    frame_detections.append((boxes, confidences, ids))

                    for box, conf, person_id in zip(boxes, confidences, ids):
                        person_boxes[person_id].append(box)
                        person_confidences[person_id].append(conf)

                        # Crop person from frame
                        person_crop = processor._crop_person(frame, box)
                        if person_crop is not None:
                            person_crops[person_id].append(person_crop)

                except Exception as e:
                    # Ignore YOLO NMS errors and other detection errors
                    error_msg = str(e).lower()
                    if 'nms' not in error_msg and 'cuda' not in error_msg:
                        print(f"⚠️  Detection warning in frame {frame_idx}: {e}")
                    frame_detections.append(([], [], []))

            # Make predictions for each person
            person_predictions = {}
            for person_id, crops in person_crops.items():
                if len(crops) >= 8:  # Minimum frames for prediction
                    try:
                        # Save temporary clip for prediction
                        temp_clip_path = processor._save_person_clip(crops, person_id, timestamp)

                        # Make prediction
                        prediction = processor._predict_clip(temp_clip_path)
                        person_predictions[person_id] = {
                            'prediction': prediction,
                            'box': person_boxes[person_id][-1],  # Latest box
                            'confidence': sum(person_confidences[person_id]) / len(person_confidences[person_id])
                        }

                        # Clean up temp file
                        import os
                        if temp_clip_path and os.path.exists(temp_clip_path):
                            os.remove(temp_clip_path)

                        # Print result
                        status = "🚨 SUSPICIOUS" if prediction > 0.5 else "✅ NORMAL"
                        print(f"{status} - Person {person_id} in clip {timestamp}: score {prediction:.3f}")

                    except Exception as e:
                        print(f"❌ Error predicting person {person_id} in clip {timestamp}: {e}")

            # Store annotated frames in memory
            if person_predictions:
                annotated_frames = []
                for frame_idx, frame in enumerate(frames):
                    annotated_frame = frame.copy()

                    # Get detections for this frame
                    if frame_idx < len(frame_detections):
                        boxes, confidences, ids = frame_detections[frame_idx]

                        # Draw annotations
                        for box, conf, person_id in zip(boxes, confidences, ids):
                            if person_id in person_predictions:
                                prediction_score = person_predictions[person_id]['prediction']
                                x1, y1, x2, y2 = map(int, box)

                                # Choose color based on prediction
                                if prediction_score > 0.5:
                                    color = (0, 0, 255)  # Red for suspicious
                                    label = f"Person {person_id}: SUSPICIOUS ({prediction_score:.2f})"
                                else:
                                    color = (0, 255, 0)  # Green for normal
                                    label = f"Person {person_id}: NORMAL ({prediction_score:.2f})"

                                # Draw bounding box and label
                                cv2.rectangle(annotated_frame, (x1, y1), (x2, y2), color, 2)
                                label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
                                cv2.rectangle(annotated_frame, (x1, y1 - label_size[1] - 10),
                                             (x1 + label_size[0], y1), color, -1)
                                cv2.putText(annotated_frame, label, (x1, y1 - 5),
                                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

                    annotated_frames.append(annotated_frame)

                # Store in memory
                self.annotated_clips_memory[video_index].append({
                    'timestamp': timestamp,
                    'frames': annotated_frames,
                    'predictions': person_predictions
                })

            # Return predictions for real-time display
            display_predictions = {}
            for person_id, data in person_predictions.items():
                display_predictions[str(person_id)] = {
                    'prediction': data['prediction'],
                    'timestamp': time.time(),
                    'box': data['box']
                }

            return display_predictions

        except Exception as e:
            print(f"❌ Error in _process_clip_memory: {e}")
            return {}

    def _process_single_video(self, video_path: str, video_index: int):
        """Process a single video in a separate thread"""
        try:
            video_name = Path(video_path).stem
            print(f"🎬 Starting processing for: {video_name}")

            # Create processor with custom output directory and thread-specific settings
            output_dir = f"data/output/multi_video_{int(time.time())}_{video_index}"

            # Each thread gets its own processor with independent model instances
            processor = VideoFileProcessor(
                video_path=video_path,
                model_path='data/models/trained_model_cnn.pt',  # Use CNN model
                yolo_path='data/models/yolo11s.pt',  # Explicitly specify YOLO path
                output_dir=output_dir,
                skip_frames=4,  # Process every 5th frame for better performance
                confidence_threshold=0.3  # Higher threshold for better performance
            )

            print(f"🧠 Thread {video_index} ({video_name}): Models loaded independently")

            self.processors[video_index] = processor
            self.frame_locks[video_index] = threading.Lock()
            self.videos_finished[video_index] = False
            self.current_predictions[video_index] = {}
            self.annotated_clips_memory[video_index] = []  # Store clips in memory

            # Open video for display frames at 16 FPS
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                print(f"❌ Could not open video: {video_path}")
                self.videos_finished[video_index] = True
                return

            frame_count = 0
            frame_delay = 1.0 / 16.0  # 16 FPS display rate
            last_frame_time = time.time()

            # Initialize frame buffer for clip processing
            frame_buffer = []
            buffer_size = 16  # frames per clip
            clip_count = 0

            # Process video frame by frame for display and predictions
            while self.running and cap.isOpened():
                ret, frame = cap.read()
                if not ret:
                    break

                frame_count += 1

                # Add frame to buffer for processing
                frame_buffer.append(frame.copy())

                # Process clip when buffer is full
                if len(frame_buffer) >= buffer_size:
                    try:
                        # Process the clip for predictions (store in memory, don't save)
                        timestamp = f"{int(time.time() * 1000)}_{clip_count}"
                        clip_predictions = self._process_clip_memory(processor, frame_buffer, timestamp, video_index)

                        # Update current predictions for this video
                        if clip_predictions:
                            self.current_predictions[video_index].update(clip_predictions)
                            # Keep only recent predictions (last 10 seconds)
                            current_time = time.time()
                            self.current_predictions[video_index] = {
                                k: v for k, v in self.current_predictions[video_index].items()
                                if current_time - v.get('timestamp', 0) < 10.0
                            }

                        clip_count += 1
                        frame_buffer = []  # Clear buffer

                    except Exception as e:
                        print(f"❌ Error processing clip for {video_name}: {e}")
                        frame_buffer = []

                # Control frame rate to 16 FPS for display
                current_time = time.time()
                if current_time - last_frame_time < frame_delay:
                    continue
                last_frame_time = current_time

                # Skip frames for display performance (show every 5th frame)
                if frame_count % 2 != 0:
                    continue

                # Create annotated frame for display
                try:
                    # Use the current predictions for this video
                    current_predictions = self.current_predictions[video_index]

                    # Detect persons and create display frame
                    annotated_frame = processor._create_display_frame(frame.copy(), current_predictions)

                    # Resize to tile size
                    annotated_frame = cv2.resize(annotated_frame, (self.tile_width, self.tile_height))

                    # Add video name overlay
                    cv2.putText(annotated_frame, video_name, (5, 20),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)

                    # Update current frame thread-safely
                    with self.frame_locks[video_index]:
                        self.current_frames[video_index] = annotated_frame

                except Exception as e:
                    # Ignore YOLO NMS errors and other common threading issues
                    error_msg = str(e).lower()
                    if 'nms' not in error_msg and 'cuda' not in error_msg and 'thread' not in error_msg:
                        print(f"⚠️  Display frame warning for {video_name}: {e}")

                    # Use original frame if processing fails
                    frame_resized = cv2.resize(frame, (self.tile_width, self.tile_height))
                    cv2.putText(frame_resized, video_name, (5, 20),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
                    with self.frame_locks[video_index]:
                        self.current_frames[video_index] = frame_resized

            cap.release()

            # Process any remaining frames in buffer
            if frame_buffer:
                try:
                    timestamp = f"{int(time.time() * 1000)}_final"
                    self._process_clip_memory(processor, frame_buffer, timestamp, video_index)
                except Exception as e:
                    print(f"❌ Error processing final clip for {video_name}: {e}")

            print(f"📊 {video_name} - Processed {clip_count} clips, stored {len(self.annotated_clips_memory[video_index])} annotated clips in memory")

            # Store performance stats for this thread
            if hasattr(processor, 'get_performance_stats'):
                thread_stats = processor.get_performance_stats()
                thread_stats['video_name'] = video_name
                thread_stats['video_index'] = video_index
                thread_stats['clip_count'] = clip_count
                thread_stats['frame_count'] = frame_count

                # Store raw timing data for aggregation
                thread_stats['inference_times'] = processor.inference_times.copy()
                thread_stats['yolo_times'] = processor.yolo_times.copy()
                thread_stats['frame_processing_times'] = processor.frame_processing_times.copy()

                self.thread_performance_stats[video_index] = thread_stats

                # Print thread-specific performance summary
                if thread_stats['inference']['total_inferences'] > 0:
                    avg_inference = thread_stats['inference']['average_time'] * 1000
                    print(f"⚡ {video_name} - Avg inference: {avg_inference:.1f}ms, "
                          f"Avg YOLO: {thread_stats['yolo']['average_time']*1000:.1f}ms")

            # Mark video as finished
            self.videos_finished[video_index] = True
            print(f"✅ Finished processing: {video_name}")

        except Exception as e:
            print(f"❌ Error in video processing thread for {video_path}: {e}")
            self.videos_finished[video_index] = True
    
    def _create_grid_frame(self) -> np.ndarray:
        """Create the grid display frame from all current video frames"""
        # Create black background
        grid_frame = np.zeros((MAX_WINDOW_HEIGHT, MAX_WINDOW_WIDTH, 3), dtype=np.uint8)
        
        for i, video_path in enumerate(self.video_files):
            row = i // self.grid_cols
            col = i % self.grid_cols
            
            # Calculate position in grid
            x = col * (self.tile_width + GRID_PADDING)
            y = row * (self.tile_height + GRID_PADDING)
            
            # Get current frame for this video
            frame = None
            if i in self.current_frames and i in self.frame_locks:
                with self.frame_locks[i]:
                    if i in self.current_frames:
                        frame = self.current_frames[i].copy()
            
            if frame is not None:
                # Place frame in grid
                grid_frame[y:y+self.tile_height, x:x+self.tile_width] = frame
            else:
                # Show placeholder with video name
                video_name = Path(video_path).stem
                placeholder = np.zeros((self.tile_height, self.tile_width, 3), dtype=np.uint8)
                cv2.putText(placeholder, f"Loading...", (10, self.tile_height//2),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (100, 100, 100), 2)
                cv2.putText(placeholder, video_name, (10, self.tile_height//2 + 30),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (150, 150, 150), 1)
                grid_frame[y:y+self.tile_height, x:x+self.tile_width] = placeholder
        
        return grid_frame

    def _save_all_annotated_clips(self):
        """Save all annotated clips from memory to disk and combine them"""
        print("\n🎬 Saving all annotated clips from memory...")

        for video_index, video_path in enumerate(self.video_files):
            if video_index not in self.annotated_clips_memory:
                continue

            video_name = Path(video_path).stem
            clips_data = self.annotated_clips_memory[video_index]

            if not clips_data:
                print(f"⚠️  No clips to save for {video_name}")
                continue

            print(f"💾 Saving {len(clips_data)} clips for {video_name}...")

            try:
                processor = self.processors[video_index]
                saved_clips = []

                # Save each clip
                for clip_data in clips_data:
                    timestamp = clip_data['timestamp']
                    frames = clip_data['frames']

                    # Save annotated clip
                    annotated_filename = f"annotated_{timestamp}.mp4"
                    annotated_path = os.path.join(processor.annotated_dir, annotated_filename)

                    # Create video writer
                    h, w = frames[0].shape[:2]
                    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
                    out = cv2.VideoWriter(annotated_path, fourcc, 16.0, (w, h))

                    for frame in frames:
                        out.write(frame)

                    out.release()
                    saved_clips.append(annotated_path)
                    print(f"📹 Saved annotated clip: {annotated_path}")

                # Combine all clips for this video
                if saved_clips:
                    combined_path = processor.combine_results()
                    if combined_path:
                        print(f"🎬 {video_name} - Combined video saved: {combined_path}")

            except Exception as e:
                print(f"❌ Error saving clips for {video_name}: {e}")

    def process_videos(self):
        """Process all videos and display grid"""
        if not self.video_files:
            print("❌ No video files found in the specified folder")
            return
        
        print(f"\n🚀 Starting multi-video processing...")
        print(f"📺 Press 'q' to quit, 'p' to pause/resume")

        self.running = True
        self.session_start_time = time.time()
        
        # Start processing threads
        threads = []
        for i, video_path in enumerate(self.video_files):
            thread = threading.Thread(
                target=self._process_single_video,
                args=(video_path, i),
                daemon=True
            )
            thread.start()
            threads.append(thread)
        
        # Display loop
        cv2.namedWindow('Multi-Video Processing Grid', cv2.WINDOW_AUTOSIZE)
        paused = False
        
        try:
            while self.running:
                if not paused:
                    # Create and display grid frame
                    grid_frame = self._create_grid_frame()
                    cv2.imshow('Multi-Video Processing Grid', grid_frame)

                # Check if all videos are finished
                if all(self.videos_finished.get(i, False) for i in range(len(self.video_files))):
                    print("✅ All videos finished processing - auto-quitting in 3 seconds...")
                    time.sleep(3)
                    break

                # Handle keyboard input
                key = cv2.waitKey(30) & 0xFF
                if key == ord('q'):
                    print("🛑 User requested quit")
                    break
                elif key == ord('p'):
                    paused = not paused
                    status = "⏸️ PAUSED" if paused else "▶️ RESUMED"
                    print(f"{status} - Press 'p' again to toggle")
        
        finally:
            self.running = False
            cv2.destroyAllWindows()

            # Wait for threads to finish
            print("⏳ Waiting for processing threads to finish...")
            for thread in threads:
                thread.join(timeout=2.0)

            # Record session end time
            self.session_end_time = time.time()

            # Save all annotated clips from memory
            self._save_all_annotated_clips()

            # Print comprehensive performance statistics
            self._print_session_performance_stats()

            print("✅ Multi-video processing completed!")

    def _print_session_performance_stats(self):
        """Print comprehensive performance statistics for the entire session"""
        if not self.thread_performance_stats or not self.session_start_time or not self.session_end_time:
            print("⚠️  No performance data available")
            return

        session_duration = self.session_end_time - self.session_start_time

        print(f"\n🚀 Multi-Video Session Performance Statistics:")
        print(f"=" * 80)

        # Session overview
        print(f"📊 Session Overview:")
        print(f"   Total session time: {session_duration:.2f}s")
        print(f"   Videos processed: {len(self.thread_performance_stats)}")
        print(f"   Threads used: {len(self.thread_performance_stats)}")

        # Aggregate statistics
        total_inferences = 0
        total_yolo_detections = 0
        total_frames = 0
        total_clips = 0
        all_inference_times = []
        all_yolo_times = []
        all_frame_times = []

        # Per-thread summary
        print(f"\n📹 Per-Video Performance:")
        for video_index, stats in self.thread_performance_stats.items():
            video_name = stats.get('video_name', f'Video_{video_index}')
            clip_count = stats.get('clip_count', 0)
            frame_count = stats.get('frame_count', 0)

            # Aggregate data
            total_inferences += stats['inference']['total_inferences']
            total_yolo_detections += stats['yolo']['total_detections']
            total_frames += frame_count
            total_clips += clip_count

            if stats['inference']['total_inferences'] > 0:
                all_inference_times.extend([t for t in stats.get('inference_times', [])])
            if stats['yolo']['total_detections'] > 0:
                all_yolo_times.extend([t for t in stats.get('yolo_times', [])])
            if stats['frame_processing']['total_frames'] > 0:
                all_frame_times.extend([t for t in stats.get('frame_processing_times', [])])

            # Print per-video stats
            print(f"   🎬 {video_name}:")
            print(f"      Clips: {clip_count}, Frames: {frame_count}")
            if stats['inference']['total_inferences'] > 0:
                print(f"      Inferences: {stats['inference']['total_inferences']} "
                      f"(avg: {stats['inference']['average_time']*1000:.1f}ms)")
            if stats['yolo']['total_detections'] > 0:
                print(f"      YOLO detections: {stats['yolo']['total_detections']} "
                      f"(avg: {stats['yolo']['average_time']*1000:.1f}ms)")

        # Overall aggregated performance
        print(f"\n🧠 Aggregated Model Performance:")
        if total_inferences > 0:
            avg_inference_time = sum(all_inference_times) / len(all_inference_times) if all_inference_times else 0
            print(f"   Total inferences: {total_inferences}")
            print(f"   Average inference time: {avg_inference_time*1000:.1f}ms")
            print(f"   Inference throughput: {total_inferences/session_duration:.2f} inferences/sec")

            if all_inference_times:
                sorted_times = sorted(all_inference_times)
                n = len(sorted_times)
                print(f"   Inference P50: {sorted_times[n//2]*1000:.1f}ms")
                print(f"   Inference P95: {sorted_times[int(n*0.95)]*1000:.1f}ms")

        if total_yolo_detections > 0:
            avg_yolo_time = sum(all_yolo_times) / len(all_yolo_times) if all_yolo_times else 0
            print(f"\n👁️  YOLO Detection Performance:")
            print(f"   Total detections: {total_yolo_detections}")
            print(f"   Average detection time: {avg_yolo_time*1000:.1f}ms")
            print(f"   Detection throughput: {total_yolo_detections/session_duration:.2f} detections/sec")

            if all_yolo_times:
                sorted_times = sorted(all_yolo_times)
                n = len(sorted_times)
                print(f"   YOLO P50: {sorted_times[n//2]*1000:.1f}ms")
                print(f"   YOLO P95: {sorted_times[int(n*0.95)]*1000:.1f}ms")

        # Overall throughput
        print(f"\n📊 Overall Throughput:")
        print(f"   Total frames processed: {total_frames}")
        print(f"   Total clips created: {total_clips}")
        print(f"   Frame processing rate: {total_frames/session_duration:.2f} frames/sec")
        print(f"   Clip processing rate: {total_clips/session_duration:.2f} clips/sec")

        # Efficiency metrics
        if total_inferences > 0 and total_yolo_detections > 0:
            total_ai_time = sum(all_inference_times) + sum(all_yolo_times)
            ai_efficiency = (total_ai_time / (session_duration * len(self.thread_performance_stats))) * 100

            print(f"\n⚡ Efficiency Metrics:")
            print(f"   Total AI processing time: {total_ai_time:.2f}s")
            print(f"   AI efficiency per thread: {ai_efficiency:.1f}%")
            print(f"   Parallel speedup: {len(self.thread_performance_stats):.1f}x")

        print(f"=" * 80)


def main():
    """Main function"""
    try:
        print(f"🚀 Starting Multi-Video Processor...")
        print(f"📁 Looking for videos in: {INPUT_FOLDER}")

        # Create processor
        processor = MultiVideoProcessor(INPUT_FOLDER, max_videos=4)

        # Process videos
        processor.process_videos()

    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return 1

    return 0


if __name__ == "__main__":
    exit(main())
