#!/usr/bin/env python3
"""
Threaded Video Processor with Queue-based Architecture
Uses separate threads for clip generation, person detection, prediction, and display
"""

import cv2
import os
import time
import threading
import queue
import numpy as np
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from collections import defaultdict
import torch

# Import required modules
from utils.detection import detect_persons
from utils.cropping import crop_person
from utils.models import load_action_transformer
from utils.prediction import predict_with_timing

# ============================================================================
# 📹 CONFIGURATION:
# ============================================================================
DEFAULT_VIDEO_PATH = "data/feed/vid2.mp4"
OUTPUT_DIR = "data/output/threaded_processing"
MODEL_PATH = 'data/models/trained_model_cnn.pt'
YOLO_PATH = 'data/models/yolo11s.pt'

# Display settings
DISPLAY_WINDOW_WIDTH = 1920
DISPLAY_WINDOW_HEIGHT = 1080
DISPLAY_FRAMERATE = 25
SHOW_PERSON_IDS = True

# Processing settings
CLIP_FRAMES = 16
CONFIDENCE_THRESHOLD = 0.3
CROP_SIZE = (224, 224)
QUEUE_MAX_SIZE = 50
# ============================================================================


class PerformanceTracker:
    """Track performance statistics across all threads"""
    
    def __init__(self):
        self.lock = threading.Lock()
        self.stats = {
            'clips_generated': 0,
            'persons_detected': 0,
            'predictions_made': 0,
            'frames_displayed': 0,
            'clip_generation_times': [],
            'detection_times': [],
            'prediction_times': [],
            'display_times': [],
            'start_time': time.time()
        }
    
    def add_clip_time(self, duration: float):
        with self.lock:
            self.stats['clips_generated'] += 1
            self.stats['clip_generation_times'].append(duration)
    
    def add_detection_time(self, duration: float, persons_count: int):
        with self.lock:
            self.stats['persons_detected'] += persons_count
            self.stats['detection_times'].append(duration)
    
    def add_prediction_time(self, duration: float):
        with self.lock:
            self.stats['predictions_made'] += 1
            self.stats['prediction_times'].append(duration)
    
    def add_display_time(self, duration: float):
        with self.lock:
            self.stats['frames_displayed'] += 1
            self.stats['display_times'].append(duration)
    
    def get_stats(self) -> Dict:
        with self.lock:
            total_time = time.time() - self.stats['start_time']
            
            def safe_avg(times_list):
                return sum(times_list) / len(times_list) if times_list else 0
            
            return {
                'total_time': total_time,
                'clips_generated': self.stats['clips_generated'],
                'persons_detected': self.stats['persons_detected'],
                'predictions_made': self.stats['predictions_made'],
                'frames_displayed': self.stats['frames_displayed'],
                'avg_clip_time': safe_avg(self.stats['clip_generation_times']),
                'avg_detection_time': safe_avg(self.stats['detection_times']),
                'avg_prediction_time': safe_avg(self.stats['prediction_times']),
                'avg_display_time': safe_avg(self.stats['display_times']),
                'clip_fps': self.stats['clips_generated'] / total_time if total_time > 0 else 0,
                'detection_fps': len(self.stats['detection_times']) / total_time if total_time > 0 else 0,
                'prediction_fps': self.stats['predictions_made'] / total_time if total_time > 0 else 0,
                'display_fps': self.stats['frames_displayed'] / total_time if total_time > 0 else 0
            }


class ThreadedVideoProcessor:
    """Main processor with queue-based multithreading architecture"""
    
    def __init__(self, video_path: str):
        self.video_path = video_path
        self.session_timestamp = time.strftime("%d-%m-%Y_%H-%M-%S")
        self.session_dir = os.path.join(OUTPUT_DIR, f"session_{self.session_timestamp}")
        self.annotated_dir = os.path.join(self.session_dir, "annotated")
        
        # Create directories
        os.makedirs(self.session_dir, exist_ok=True)
        os.makedirs(self.annotated_dir, exist_ok=True)
        
        # Initialize queues
        self.clip_queue = queue.Queue(maxsize=QUEUE_MAX_SIZE)
        self.person_queue = queue.Queue(maxsize=QUEUE_MAX_SIZE)
        self.prediction_queue = queue.Queue(maxsize=QUEUE_MAX_SIZE)
        self.display_queue = queue.Queue(maxsize=QUEUE_MAX_SIZE)
        
        # Control flags
        self.stop_event = threading.Event()
        self.pause_event = threading.Event()
        self.video_ended = threading.Event()  # Signal when video reaches end
        
        # Performance tracking
        self.perf_tracker = PerformanceTracker()
        
        # Models (will be loaded in respective threads)
        self.device = None
        self.action_model = None
        self.yolo_model = None
        
        # Storage for final video combination
        self.annotated_clips = []
        
        print(f"📁 Session directory: {self.session_dir}")
    
    def initialize_models(self):
        """Initialize CUDA-only models in half precision with evaluation mode"""
        # Force CUDA usage - fail if not available
        if not torch.cuda.is_available():
            raise RuntimeError("❌ CUDA is not available! This application requires a CUDA-capable GPU.")

        if torch.cuda.device_count() == 0:
            raise RuntimeError("❌ No CUDA devices found! This application requires a CUDA-capable GPU.")

        # Force CUDA device
        self.device = torch.device('cuda:0')
        torch.cuda.set_device(0)  # Explicitly set CUDA device

        print(f"🚀 Using device: {self.device} ({torch.cuda.get_device_name(0)})")
        print(f"🔥 CUDA Memory: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
        print(f"⚡ Half precision mode: ENABLED")

        # Load CNN model and force to CUDA with half precision
        print("Loading CNN model...")
        self.action_model = load_action_transformer(MODEL_PATH, self.device)
        if self.action_model is None:
            raise RuntimeError("❌ Failed to load CNN model!")

        # Force model to CUDA and half precision
        self.action_model = self.action_model.to(self.device)
        self.action_model = self.action_model.half()  # Convert to half precision
        self.action_model.eval()  # Set to evaluation mode

        # Set backbone to evaluation mode if it exists
        if hasattr(self.action_model, 'backbone'):
            self.action_model.backbone.eval()
            print("✅ CNN backbone set to evaluation mode")

        # Load YOLO model and force to CUDA with half precision
        print("Loading YOLO model...")
        from ultralytics import YOLO
        self.yolo_model = YOLO(YOLO_PATH)
        if self.yolo_model is None:
            raise RuntimeError("❌ Failed to load YOLO model!")

        # Force YOLO to CUDA and half precision
        self.yolo_model.to(self.device)
        self.yolo_model.half()  # Convert to half precision

        # Set YOLO model to evaluation mode
        if hasattr(self.yolo_model, 'model'):
            self.yolo_model.model.eval()
            print("✅ YOLO model set to evaluation mode")

        # Verify models are on CUDA and in half precision
        if next(self.action_model.parameters()).device.type != 'cuda':
            raise RuntimeError("❌ CNN model is not on CUDA!")

        if next(self.action_model.parameters()).dtype != torch.float16:
            raise RuntimeError("❌ CNN model is not in half precision!")

        print("✅ All models loaded successfully on CUDA with half precision!")
        print(f"✅ CNN model device: {next(self.action_model.parameters()).device}")
        print(f"✅ CNN model dtype: {next(self.action_model.parameters()).dtype}")
        print(f"✅ CNN model eval mode: {not self.action_model.training}")
        print(f"✅ YOLO model device: {self.yolo_model.device}")

        # Verify backbone if exists
        if hasattr(self.action_model, 'backbone'):
            print(f"✅ CNN backbone eval mode: {not self.action_model.backbone.training}")

        # Perform CUDA verification test
        self._verify_cuda_setup()

        # Perform model and GPU warmup
        self._warmup_models()

    def _verify_cuda_setup(self):
        """Verify CUDA setup with half precision test"""
        print("🔍 Verifying CUDA setup with half precision...")

        try:
            # Test CUDA tensor operations in half precision
            test_tensor = torch.randn(1, 3, 16, 112, 112, device=self.device, dtype=torch.float16)

            # Test CNN model on CUDA with half precision
            with torch.no_grad():
                test_output = self.action_model(test_tensor)
                if test_output.device.type != 'cuda':
                    raise RuntimeError("❌ CNN model output is not on CUDA!")
                if test_output.dtype != torch.float16:
                    raise RuntimeError("❌ CNN model output is not in half precision!")

            # Test YOLO model on CUDA
            test_frame = torch.randint(0, 255, (480, 640, 3), dtype=torch.uint8).cpu().numpy()
            _ = self.yolo_model(test_frame, verbose=False)  # Test YOLO inference

            print("✅ CUDA verification successful!")
            print(f"✅ Test tensor device: {test_tensor.device}")
            print(f"✅ Test tensor dtype: {test_tensor.dtype}")
            print(f"✅ CNN output device: {test_output.device}")
            print(f"✅ CNN output dtype: {test_output.dtype}")
            print(f"✅ Half precision inference working correctly!")

        except Exception as e:
            raise RuntimeError(f"❌ CUDA verification failed: {e}")

    def _warmup_models(self):
        """Warmup YOLO, CNN model, and GPU before processing starts"""
        print("🔥 Warming up models and GPU...")

        try:
            # GPU warmup - allocate and deallocate memory to initialize GPU
            print("🔥 GPU warmup...")
            warmup_tensor = torch.randn(1000, 1000, device=self.device, dtype=torch.float16)
            _ = torch.matmul(warmup_tensor, warmup_tensor.T)
            del warmup_tensor
            torch.cuda.empty_cache()
            print("✅ GPU warmup completed")

            # YOLO model warmup
            print("🔥 YOLO model warmup...")
            import numpy as np

            # Create multiple test frames for thorough warmup
            for i in range(3):
                warmup_frame = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)

                # Ensure YOLO is in eval mode
                if hasattr(self.yolo_model, 'model'):
                    self.yolo_model.model.eval()

                # Run YOLO inference
                _ = self.yolo_model(warmup_frame, verbose=False)

            print("✅ YOLO model warmup completed")

            # CNN model warmup
            print("🔥 CNN model warmup...")

            # Ensure models are in eval mode
            self.action_model.eval()
            if hasattr(self.action_model, 'backbone'):
                self.action_model.backbone.eval()

            # Create test tensors for CNN warmup
            for i in range(3):
                warmup_input = torch.randn(1, 3, 16, 112, 112, device=self.device, dtype=torch.float16)

                with torch.no_grad():
                    with torch.cuda.device(self.device):
                        _ = self.action_model(warmup_input)

                del warmup_input

            torch.cuda.empty_cache()
            print("✅ CNN model warmup completed")

            # Final memory cleanup
            torch.cuda.empty_cache()

            print("🚀 All models warmed up and ready for processing!")

        except Exception as e:
            print(f"⚠️  Warmup warning: {e}")
            print("🔄 Continuing with processing...")
    
    def clip_generation_thread(self):
        """Thread 1: Generate video clips and add to queue (NO FRAME SKIPPING)"""
        print("🎬 Starting clip generation thread (processing every frame)...")

        cap = cv2.VideoCapture(self.video_path)
        if not cap.isOpened():
            print(f"❌ Could not open video: {self.video_path}")
            return

        frame_buffer = []
        frame_count = 0
        
        while not self.stop_event.is_set():
            if self.pause_event.is_set():
                time.sleep(0.1)
                continue
            
            ret, frame = cap.read()
            if not ret:
                print("📹 Video ended - signaling completion")
                self.video_ended.set()  # Signal that video has ended
                break
            
            frame_count += 1
            frame_buffer.append(frame.copy())
            
            # When buffer is full, create a clip
            if len(frame_buffer) == CLIP_FRAMES:
                start_time = time.time()
                
                clip_data = {
                    'frames': frame_buffer.copy(),
                    'timestamp': f"{int(time.time() * 1000)}",
                    'frame_start': frame_count - CLIP_FRAMES,
                    'frame_end': frame_count
                }
                
                try:
                    self.clip_queue.put(clip_data, timeout=1.0)
                    clip_time = time.time() - start_time
                    self.perf_tracker.add_clip_time(clip_time)
                except queue.Full:
                    print("⚠️  Clip queue full, dropping clip")
                
                frame_buffer.clear()

        # Process any remaining frames in buffer when video ends
        if frame_buffer and len(frame_buffer) >= 4:  # Minimum frames for processing
            print(f"📦 Processing final clip with {len(frame_buffer)} frames")
            start_time = time.time()

            clip_data = {
                'frames': frame_buffer.copy(),
                'timestamp': f"{int(time.time() * 1000)}_final",
                'frame_start': frame_count - len(frame_buffer),
                'frame_end': frame_count
            }

            try:
                self.clip_queue.put(clip_data, timeout=1.0)
                clip_time = time.time() - start_time
                self.perf_tracker.add_clip_time(clip_time)
            except queue.Full:
                print("⚠️  Clip queue full, dropping final clip")

        cap.release()

        # Signal that clip generation is completely finished
        print("✅ Clip generation thread finished - all clips queued")

        # Add a small delay to ensure the final clip is processed
        time.sleep(1.0)
    
    def person_detection_thread(self):
        """Thread 2: Detect persons and crop from clips (EVERY FRAME PROCESSED)"""
        print("👁️  Starting person detection thread (processing every frame)...")

        while not self.stop_event.is_set():
            try:
                clip_data = self.clip_queue.get(timeout=1.0)
            except queue.Empty:
                # Only exit if explicitly stopped - let the queue drain completely
                continue

            start_time = time.time()

            # Process each frame in the clip (no frame skipping)
            person_crops = defaultdict(list)
            person_boxes = defaultdict(list)
            frame_detections = []
            
            for frame_idx, frame in enumerate(clip_data['frames']):
                try:
                    # Ensure YOLO model is in evaluation mode
                    if hasattr(self.yolo_model, 'model'):
                        self.yolo_model.model.eval()

                    boxes, confidences, ids = detect_persons(self.yolo_model, frame, CONFIDENCE_THRESHOLD)
                    frame_detections.append({
                        'frame_idx': frame_idx,
                        'boxes': boxes,
                        'confidences': confidences,
                        'ids': ids
                    })
                    
                    # Crop persons
                    for box, conf, person_id in zip(boxes, confidences, ids):
                        if conf > CONFIDENCE_THRESHOLD:
                            crop = crop_person(frame, box, CROP_SIZE)
                            if crop is not None:
                                person_crops[person_id].append(crop)
                                person_boxes[person_id].append(box)
                
                except Exception as e:
                    # Ignore YOLO errors
                    if 'nms' not in str(e).lower():
                        print(f"⚠️  Detection warning: {e}")
                    frame_detections.append({
                        'frame_idx': frame_idx,
                        'boxes': [], 'confidences': [], 'ids': []
                    })
            
            # Create person clip data
            person_data = {
                'original_clip': clip_data,
                'person_crops': dict(person_crops),
                'person_boxes': dict(person_boxes),
                'frame_detections': frame_detections,
                'timestamp': clip_data['timestamp']
            }
            
            detection_time = time.time() - start_time
            self.perf_tracker.add_detection_time(detection_time, len(person_crops))
            
            try:
                self.person_queue.put(person_data, timeout=1.0)
            except queue.Full:
                print("⚠️  Person queue full, dropping data")
            
            self.clip_queue.task_done()
        
        print("✅ Person detection thread finished")

    def prediction_thread(self):
        """Thread 3: Make predictions on person crops (ALL AVAILABLE FRAMES USED)"""
        print("🧠 Starting prediction thread (using all available frames)...")

        while not self.stop_event.is_set():
            try:
                person_data = self.person_queue.get(timeout=1.0)
            except queue.Empty:
                # Only exit if explicitly stopped - let the queue drain completely
                continue

            start_time = time.time()
            predictions = {}

            # Make predictions for each person (use all available frames, no skipping)
            for person_id, crops in person_data['person_crops'].items():
                if len(crops) >= 4:  # Minimum 4 frames for meaningful prediction (reduced from 8)
                    try:
                        # Save temporary person clip
                        temp_clip_path = self._save_temp_person_clip(crops, person_id, person_data['timestamp'])

                        # Make prediction (ensure CUDA usage with half precision)
                        with torch.cuda.device(self.device):
                            # Ensure model is in eval mode and half precision
                            self.action_model.eval()
                            if hasattr(self.action_model, 'backbone'):
                                self.action_model.backbone.eval()

                            prediction, inference_time = predict_with_timing(
                                self.action_model, temp_clip_path, self.device, warmup_runs=1
                            )

                        predictions[person_id] = {
                            'prediction': prediction,
                            'inference_time': inference_time,
                            'box': person_data['person_boxes'][person_id][-1] if person_data['person_boxes'][person_id] else None
                        }

                        # Clean up temp file
                        if os.path.exists(temp_clip_path):
                            os.remove(temp_clip_path)

                        # Print result
                        status = "🚨 SUSPICIOUS" if prediction > 0.5 else "✅ NORMAL"
                        if SHOW_PERSON_IDS:
                            print(f"{status} - Person {person_id}: {prediction:.3f}")
                        else:
                            print(f"{status} - Detection: {prediction:.3f}")

                    except Exception as e:
                        print(f"❌ Error predicting person {person_id}: {e}")

            # Create prediction data
            prediction_data = {
                'original_clip': person_data['original_clip'],
                'frame_detections': person_data['frame_detections'],
                'predictions': predictions,
                'timestamp': person_data['timestamp']
            }

            prediction_time = time.time() - start_time
            self.perf_tracker.add_prediction_time(prediction_time)

            try:
                self.prediction_queue.put(prediction_data, timeout=1.0)
            except queue.Full:
                print("⚠️  Prediction queue full, dropping data")

            self.person_queue.task_done()

        print("✅ Prediction thread finished")

    def display_thread(self):
        """Thread 4: Display annotated video and save clips (ALL FRAMES DISPLAYED)"""
        print("📺 Starting display thread (displaying every frame)...")

        # Setup display window
        cv2.namedWindow('Threaded Video Processor', cv2.WINDOW_NORMAL)
        cv2.resizeWindow('Threaded Video Processor', DISPLAY_WINDOW_WIDTH, DISPLAY_WINDOW_HEIGHT)

        frame_delay = int(1000 / DISPLAY_FRAMERATE)

        while not self.stop_event.is_set():
            try:
                prediction_data = self.prediction_queue.get(timeout=1.0)
            except queue.Empty:
                # Check if video has ended and all processing is complete
                if self.video_ended.is_set():
                    # Wait a bit to ensure all threads have finished processing
                    time.sleep(0.5)

                    # Check queue states for debugging
                    clip_empty = self.clip_queue.empty()
                    person_empty = self.person_queue.empty()
                    prediction_empty = self.prediction_queue.empty()

                    print(f"🔍 Queue status - Clips: {'empty' if clip_empty else 'has data'}, "
                          f"Persons: {'empty' if person_empty else 'has data'}, "
                          f"Predictions: {'empty' if prediction_empty else 'has data'}")

                    # Check if all queues are truly empty (processing complete)
                    if clip_empty and person_empty and prediction_empty:
                        # Double-check after another short wait
                        time.sleep(1.0)
                        if (self.clip_queue.empty() and
                            self.person_queue.empty() and
                            self.prediction_queue.empty()):
                            print("🏁 All processing completed - auto-quitting in 3 seconds...")
                            time.sleep(3)
                            self.stop_event.set()
                            break
                        else:
                            print("🔄 Queues refilled during check - continuing processing...")
                    else:
                        print("⏳ Still processing - waiting for queues to empty...")

                # Handle keyboard input even when no data
                key = cv2.waitKey(1) & 0xFF
                if key == ord('q'):
                    print("🛑 User requested quit")
                    self.stop_event.set()
                elif key == ord('p'):
                    if self.pause_event.is_set():
                        self.pause_event.clear()
                        print("▶️ RESUMED")
                    else:
                        self.pause_event.set()
                        print("⏸️ PAUSED")
                continue

            start_time = time.time()

            # Create annotated frames
            annotated_frames = []
            for frame_idx, frame in enumerate(prediction_data['original_clip']['frames']):
                annotated_frame = self._create_annotated_frame(
                    frame, frame_idx, prediction_data['frame_detections'], prediction_data['predictions']
                )
                annotated_frames.append(annotated_frame)

            # Save annotated clip
            if prediction_data['predictions']:
                annotated_path = self._save_annotated_clip(annotated_frames, prediction_data['timestamp'])
                self.annotated_clips.append(annotated_path)

            # Display ALL frames sequentially (no frame skipping)
            if annotated_frames:
                for frame_idx, annotated_frame in enumerate(annotated_frames):
                    # Check for stop/pause events during frame display
                    if self.stop_event.is_set():
                        break

                    if self.pause_event.is_set():
                        # Handle pause during frame sequence
                        while self.pause_event.is_set() and not self.stop_event.is_set():
                            key = cv2.waitKey(100) & 0xFF
                            if key == ord('q'):
                                self.stop_event.set()
                                break
                            elif key == ord('p'):
                                self.pause_event.clear()
                                print("▶️ RESUMED")
                                break
                        continue

                    # Resize for display
                    display_frame = cv2.resize(annotated_frame, (DISPLAY_WINDOW_WIDTH, DISPLAY_WINDOW_HEIGHT))

                    # Add performance info (only on first frame of clip to avoid flicker)
                    if frame_idx == 0:
                        stats = self.perf_tracker.get_stats()
                        info_text = f"Clips: {stats['clips_generated']} | Persons: {stats['persons_detected']} | Predictions: {stats['predictions_made']}"
                        cv2.putText(display_frame, info_text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)

                        fps_text = f"FPS - Clip: {stats['clip_fps']:.1f} | Detection: {stats['detection_fps']:.1f} | Prediction: {stats['prediction_fps']:.1f} | Display: {stats['display_fps']:.1f}"
                        cv2.putText(display_frame, fps_text, (10, 70), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)

                        cv2.putText(display_frame, "Press 'q' to quit, 'p' to pause", (10, DISPLAY_WINDOW_HEIGHT - 20),
                                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (200, 200, 200), 2)

                    # Add frame info
                    frame_info = f"Frame {frame_idx + 1}/{len(annotated_frames)} | Clip: {prediction_data['timestamp']}"
                    cv2.putText(display_frame, frame_info, (10, DISPLAY_WINDOW_HEIGHT - 50),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)

                    cv2.imshow('Threaded Video Processor', display_frame)

                    # Handle keyboard input for each frame
                    key = cv2.waitKey(frame_delay) & 0xFF
                    if key == ord('q'):
                        print("🛑 User requested quit")
                        self.stop_event.set()
                        break
                    elif key == ord('p'):
                        if self.pause_event.is_set():
                            self.pause_event.clear()
                            print("▶️ RESUMED")
                        else:
                            self.pause_event.set()
                            print("⏸️ PAUSED")

            display_time = time.time() - start_time
            self.perf_tracker.add_display_time(display_time)

            self.prediction_queue.task_done()

        cv2.destroyAllWindows()
        print("✅ Display thread finished")

    def _save_temp_person_clip(self, crops: List, person_id: int, timestamp: str) -> str:
        """Save temporary person clip for prediction"""
        filename = f"temp_person_{person_id}_{timestamp}.mp4"
        temp_path = os.path.join(self.session_dir, filename)

        if not crops:
            return None

        h, w = crops[0].shape[:2]
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(temp_path, fourcc, 16.0, (w, h))

        for crop in crops:
            out.write(crop)

        out.release()
        return temp_path

    def _create_annotated_frame(self, frame: np.ndarray, frame_idx: int,
                               frame_detections: List, predictions: Dict) -> np.ndarray:
        """Create annotated frame with bounding boxes and predictions"""
        annotated_frame = frame.copy()

        # Get detections for this frame
        if frame_idx < len(frame_detections):
            detection = frame_detections[frame_idx]
            boxes = detection['boxes']
            confidences = detection['confidences']
            ids = detection['ids']

            # Draw bounding boxes
            for box, conf, person_id in zip(boxes, confidences, ids):
                if person_id in predictions:
                    prediction_score = predictions[person_id]['prediction']
                    x1, y1, x2, y2 = map(int, box)

                    # Choose color based on prediction
                    if prediction_score > 0.5:
                        color = (0, 0, 255)  # Red for suspicious
                        if SHOW_PERSON_IDS:
                            label = f"Person {person_id}: SUSPICIOUS ({prediction_score:.2f})"
                        else:
                            label = f"SUSPICIOUS ({prediction_score:.2f})"
                    else:
                        color = (0, 255, 0)  # Green for normal
                        if SHOW_PERSON_IDS:
                            label = f"Person {person_id}: NORMAL ({prediction_score:.2f})"
                        else:
                            label = f"NORMAL ({prediction_score:.2f})"

                    # Draw bounding box
                    cv2.rectangle(annotated_frame, (x1, y1), (x2, y2), color, 2)

                    # Draw label background
                    label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
                    cv2.rectangle(annotated_frame, (x1, y1 - label_size[1] - 10),
                                 (x1 + label_size[0], y1), color, -1)

                    # Draw label text
                    cv2.putText(annotated_frame, label, (x1, y1 - 5),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

                    # Draw confidence
                    conf_text = f"Conf: {conf:.2f}"
                    cv2.putText(annotated_frame, conf_text, (x1, y2 + 20),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)

        return annotated_frame

    def _save_annotated_clip(self, frames: List, timestamp: str) -> str:
        """Save annotated clip to file"""
        filename = f"annotated_{timestamp}.mp4"
        output_path = os.path.join(self.annotated_dir, filename)

        if not frames:
            return None

        h, w = frames[0].shape[:2]
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(output_path, fourcc, 16.0, (w, h))

        for frame in frames:
            out.write(frame)

        out.release()
        return output_path

    def combine_annotated_clips(self) -> Optional[str]:
        """Combine all annotated clips into a single video"""
        if not self.annotated_clips:
            print("⚠️  No annotated clips to combine")
            return None

        print(f"🎬 Combining {len(self.annotated_clips)} annotated clips...")

        combined_filename = f"combined_annotated_{self.session_timestamp}.mp4"
        combined_path = os.path.join(self.session_dir, combined_filename)

        # Get video properties from first clip
        first_clip = cv2.VideoCapture(self.annotated_clips[0])
        fps = first_clip.get(cv2.CAP_PROP_FPS) or 16.0
        width = int(first_clip.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(first_clip.get(cv2.CAP_PROP_FRAME_HEIGHT))
        first_clip.release()

        # Create output video
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(combined_path, fourcc, fps, (width, height))

        try:
            for clip_path in sorted(self.annotated_clips):
                if os.path.exists(clip_path):
                    cap = cv2.VideoCapture(clip_path)

                    while True:
                        ret, frame = cap.read()
                        if not ret:
                            break
                        out.write(frame)

                    cap.release()

            out.release()
            print(f"✅ Combined video saved: {combined_path}")
            return combined_path

        except Exception as e:
            print(f"❌ Error combining clips: {e}")
            out.release()
            return None

    def process_video(self):
        """Main processing method - starts all threads and manages execution"""
        print("🚀 Starting threaded video processing...")
        print(f"📹 Video: {self.video_path}")
        print(f"📺 Display: {DISPLAY_WINDOW_WIDTH}x{DISPLAY_WINDOW_HEIGHT} @ {DISPLAY_FRAMERATE} FPS")
        print(f"👥 Person IDs: {'Enabled' if SHOW_PERSON_IDS else 'Disabled'}")
        print(f"🚫 Frame Skipping: DISABLED - Processing every frame")
        print(f"🔥 Model Warmup: ENABLED - YOLO, CNN, and GPU pre-warmed")
        print(f"🏁 Auto-quit: ENABLED - Will quit automatically when video ends")
        print(f"🎮 Controls: 'q'=quit, 'p'=pause")
        print("=" * 70)

        # Initialize models
        self.initialize_models()

        # Create and start threads
        threads = [
            threading.Thread(target=self.clip_generation_thread, name="ClipGen"),
            threading.Thread(target=self.person_detection_thread, name="PersonDetect"),
            threading.Thread(target=self.prediction_thread, name="Prediction"),
            threading.Thread(target=self.display_thread, name="Display")
        ]

        # Start all threads
        for thread in threads:
            thread.daemon = True
            thread.start()
            print(f"✅ Started {thread.name} thread")

        # Start performance monitoring
        self._start_performance_monitoring()

        try:
            # Wait for display thread (main interaction thread)
            threads[3].join()  # Display thread

            # Signal other threads to stop
            self.stop_event.set()

            # Wait for other threads to finish
            for i, thread in enumerate(threads[:-1]):  # Exclude display thread
                thread.join(timeout=5.0)
                if thread.is_alive():
                    print(f"⚠️  {thread.name} thread did not finish gracefully")

        except KeyboardInterrupt:
            print("\n🛑 Interrupted by user")
            self.stop_event.set()

        # Print final performance statistics
        self._print_final_stats()

        # Combine annotated clips
        combined_path = self.combine_annotated_clips()

        return {
            'session_dir': self.session_dir,
            'combined_video': combined_path,
            'annotated_clips': len(self.annotated_clips),
            'performance_stats': self.perf_tracker.get_stats()
        }

    def _start_performance_monitoring(self):
        """Start performance monitoring thread"""
        def monitor_performance():
            while not self.stop_event.is_set():
                time.sleep(5.0)  # Update every 5 seconds
                stats = self.perf_tracker.get_stats()

                print(f"\n📊 Performance Update:")
                print(f"   Clips: {stats['clips_generated']} ({stats['clip_fps']:.1f}/s)")
                print(f"   Persons: {stats['persons_detected']} ({stats['detection_fps']:.1f}/s)")
                print(f"   Predictions: {stats['predictions_made']} ({stats['prediction_fps']:.1f}/s)")
                print(f"   Display: {stats['frames_displayed']} ({stats['display_fps']:.1f}/s)")
                print(f"   Runtime: {stats['total_time']:.1f}s")

        monitor_thread = threading.Thread(target=monitor_performance, name="Monitor")
        monitor_thread.daemon = True
        monitor_thread.start()

    def _print_final_stats(self):
        """Print comprehensive final performance statistics"""
        stats = self.perf_tracker.get_stats()

        print(f"\n🏁 Final Performance Statistics:")
        print("=" * 70)
        print(f"📊 Processing Summary:")
        print(f"   Total runtime: {stats['total_time']:.2f}s")
        print(f"   Clips generated: {stats['clips_generated']}")
        print(f"   Persons detected: {stats['persons_detected']}")
        print(f"   Predictions made: {stats['predictions_made']}")
        print(f"   Frames displayed: {stats['frames_displayed']}")

        print(f"\n⚡ Performance Metrics:")
        print(f"   Clip generation: {stats['avg_clip_time']*1000:.1f}ms avg ({stats['clip_fps']:.2f} clips/s)")
        print(f"   Person detection: {stats['avg_detection_time']*1000:.1f}ms avg ({stats['detection_fps']:.2f} detections/s)")
        print(f"   Prediction: {stats['avg_prediction_time']*1000:.1f}ms avg ({stats['prediction_fps']:.2f} predictions/s)")
        print(f"   Display: {stats['avg_display_time']*1000:.1f}ms avg ({stats['display_fps']:.2f} frames/s)")

        print(f"\n📁 Output:")
        print(f"   Session directory: {self.session_dir}")
        print(f"   Annotated clips: {len(self.annotated_clips)}")
        print("=" * 70)


def main():
    """Main function"""
    import argparse

    parser = argparse.ArgumentParser(description='Threaded Video Processor for Shoplifting Detection')
    parser.add_argument('video_path', nargs='?', default=DEFAULT_VIDEO_PATH,
                       help=f'Path to input video file (default: {DEFAULT_VIDEO_PATH})')
    parser.add_argument('--output', default=OUTPUT_DIR,
                       help='Output directory')

    args = parser.parse_args()

    # Validate input file
    if not os.path.exists(args.video_path):
        print(f"❌ Error: Video file not found: {args.video_path}")
        return 1

    print("🎥 Threaded Video Processor for Shoplifting Detection")
    print("=" * 70)

    try:
        # Create and run processor
        processor = ThreadedVideoProcessor(args.video_path)
        results = processor.process_video()

        print(f"\n✅ Processing completed successfully!")
        print(f"📁 Results saved to: {results['session_dir']}")
        if results['combined_video']:
            print(f"🎬 Combined video: {results['combined_video']}")

        return 0

    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit(main())
