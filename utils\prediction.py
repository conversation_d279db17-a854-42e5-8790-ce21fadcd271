"""
Prediction utilities for ActionTransformer model
"""

import torch
import time
import numpy as np
import cv2
from .videodataset import load_video_tensor
from typing import Optional, List


def predict_video_clip(model, video_path: str, device, warmup_runs: int = 2) -> float:
    """
    Make prediction on a video clip using ActionTransformer model
    
    Args:
        model: ActionTransformer model instance
        video_path: Path to video clip
        device: PyTorch device
        warmup_runs: Number of warmup runs for consistent timing
    
    Returns:
        Prediction score (0.0 = normal, 1.0 = suspicious)
    """
    try:
        # Load video tensor
        input_tensor = load_video_tensor(video_path).unsqueeze(0).to(device)
        
        # Make prediction
        with torch.no_grad():
            # Warmup runs for consistent timing
            for _ in range(warmup_runs):
                _ = model(input_tensor)
            
            # Actual prediction
            output = torch.sigmoid(model(input_tensor)).item()
        
        return 1.0 if output > 0.5 else 0.0
        
    except Exception as e:
        print(f"Error making prediction on {video_path}: {e}")
        return 0.0


def predict_from_crops(model, crops: List[np.ndarray], device) -> float:
    """
    Make prediction on person crops using ActionTransformer model

    Args:
        model: ActionTransformer model instance
        crops: List of cropped person images (numpy arrays)
        device: PyTorch device

    Returns:
        Prediction score (0.0 = normal, 1.0 = suspicious)
    """
    try:
        if len(crops) < 8:  # Minimum frames required
            return 0.0

        # Convert crops to tensor format expected by model
        # Crops are expected to be (224, 224, 3) RGB images
        frames = []
        for crop in crops[:16]:  # Use up to 16 frames
            if crop.shape[:2] != (224, 224):
                crop = cv2.resize(crop, (224, 224))
            # Convert BGR to RGB if needed
            if len(crop.shape) == 3 and crop.shape[2] == 3:
                crop = cv2.cvtColor(crop, cv2.COLOR_BGR2RGB)
            frames.append(crop)

        # Pad with last frame if needed
        while len(frames) < 16:
            frames.append(frames[-1])

        # Convert to tensor: (1, 3, 16, 224, 224)
        frames_array = np.array(frames)  # (16, 224, 224, 3)
        frames_array = frames_array.transpose(3, 0, 1, 2)  # (3, 16, 224, 224)
        frames_array = frames_array.astype(np.float32) / 255.0  # Normalize

        input_tensor = torch.from_numpy(frames_array).unsqueeze(0).to(device)

        # Make prediction
        with torch.no_grad():
            model.eval()
            output = torch.sigmoid(model(input_tensor)).item()

        return output

    except Exception as e:
        # Silently return 0.0 for failed predictions
        return 0.0


def predict_with_timing(model, video_path: str, device, warmup_runs: int = 2) -> tuple:
    """
    Make prediction with timing information
    
    Args:
        model: ActionTransformer model instance
        video_path: Path to video clip
        device: PyTorch device
        warmup_runs: Number of warmup runs
    
    Returns:
        Tuple of (prediction_score, prediction_time_seconds)
    """
    try:
        # Load video tensor
        input_tensor = load_video_tensor(video_path).unsqueeze(0).to(device)
        
        with torch.no_grad():
            # Warmup runs
            for _ in range(warmup_runs):
                _ = model(input_tensor)
            
            # Timed prediction
            start_time = time.time()
            output = torch.sigmoid(model(input_tensor)).item()
            prediction_time = time.time() - start_time
        
        prediction = 1.0 if output > 0.5 else 0.0
        return prediction, prediction_time
        
    except Exception as e:
        print(f"Error making prediction on {video_path}: {e}")
        return 0.0, 0.0


def batch_predict(model, video_paths: list, device, warmup_runs: int = 2) -> list:
    """
    Make predictions on multiple video clips
    
    Args:
        model: ActionTransformer model instance
        video_paths: List of video clip paths
        device: PyTorch device
        warmup_runs: Number of warmup runs for first clip
    
    Returns:
        List of prediction scores
    """
    predictions = []
    
    for i, video_path in enumerate(video_paths):
        # Only do warmup for first clip
        warmup = warmup_runs if i == 0 else 0
        prediction = predict_video_clip(model, video_path, device, warmup)
        predictions.append(prediction)
    
    return predictions


def get_prediction_label(score: float, threshold: float = 0.5) -> str:
    """
    Convert prediction score to human-readable label
    
    Args:
        score: Prediction score (0.0 to 1.0)
        threshold: Threshold for suspicious behavior
    
    Returns:
        String label ("Normal" or "Shoplifting")
    """
    return "Shoplifting" if score > threshold else "Normal"


def get_prediction_confidence(score: float) -> str:
    """
    Get confidence level description for prediction
    
    Args:
        score: Prediction score (0.0 to 1.0)
    
    Returns:
        Confidence level string
    """
    if score > 0.8 or score < 0.2:
        return "HIGH"
    elif score > 0.6 or score < 0.4:
        return "MEDIUM"
    else:
        return "LOW"


def initialize_model(model_path: str, device) -> Optional[object]:
    """
    Initialize ActionTransformer model

    Args:
        model_path: Path to model checkpoint
        device: PyTorch device

    Returns:
        Initialized model or None if failed
    """
    try:
        from .actiontransformer import ActionTransformer

        model = ActionTransformer()
        checkpoint = torch.load(model_path, map_location=device)
        model.load_state_dict(checkpoint['model_state_dict'])
        model.to(device).eval()
        model.backbone.eval()

        print(f"✅ ActionTransformer model loaded from {model_path}")
        return model

    except Exception as e:
        print(f"❌ Error loading ActionTransformer model: {e}")
        return None
